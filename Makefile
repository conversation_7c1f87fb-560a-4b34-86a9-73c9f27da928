.PHONY: help install test backtest run docker-build docker-run clean

help:
	@echo "Available commands:"
	@echo "  make install       - Install dependencies"
	@echo "  make test          - Run unit tests"
	@echo "  make backtest      - Run strategy backtest"
	@echo "  make run           - Run bot in dry-run mode"
	@echo "  make docker-build  - Build Docker image"
	@echo "  make docker-run    - Run bot in Docker"
	@echo "  make clean         - Clean up temporary files"

install:
	pip install -r requirements.txt
	
test:
	pytest tests/ -v --cov=strategies --cov-report=html

backtest:
	freqtrade backtesting \
		--config config.json \
		--strategy GridMakerStrategy \
		--timerange 20240101-20240201 \
		--timeframe 1m

download-data:
	freqtrade download-data \
		--config config.json \
		--pairs XRP/FDUSD BTC/FDUSD ETH/FDUSD \
		--timeframe 1m 5m 15m 1h \
		--timerange 20240101-

hyperopt:
	freqtrade hyperopt \
		--config config.json \
		--strategy GridMakerStrategy \
		--hyperopt-loss SharpeHyperOptLoss \
		--epochs 100 \
		--spaces buy sell roi stoploss

run:
	freqtrade trade \
		--config config.json \
		--strategy GridMakerStrategy \
		--dry-run

docker-build:
	docker build -t freqtrade-grid-maker .

docker-run:
	docker-compose up -d

docker-logs:
	docker-compose logs -f freqtrade

docker-stop:
	docker-compose down

clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .coverage

format:
	black strategies/ tests/
	flake8 strategies/ tests/

type-check:
	mypy strategies/ --ignore-missing-imports
