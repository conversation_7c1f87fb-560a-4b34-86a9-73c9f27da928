"""
Order Manager Component
Handles order placement, sizing, and grid level management
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Order type enumeration"""
    LIMIT = "limit"
    MARKET = "market"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"


@dataclass
class GridLevel:
    """Represents a single grid level"""
    level: int
    price: float
    quantity: float
    side: OrderSide
    distance_from_mid: float
    is_active: bool = False
    order_id: Optional[str] = None
    placed_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate grid level parameters"""
        if self.price <= 0:
            raise ValueError(f"Invalid price: {self.price}")
        if self.quantity <= 0:
            raise ValueError(f"Invalid quantity: {self.quantity}")
        if self.level < 0:
            raise ValueError(f"Invalid level: {self.level}")


@dataclass
class OrderConfig:
    """Order manager configuration"""
    max_grid_levels: int = 3
    base_order_size_pct: float = 0.02  # 2% of account balance
    order_size_multiplier: float = 1.5  # Multiplier for each grid level
    min_order_value: float = 10.0  # Minimum order value in quote currency
    max_order_value: float = 10000.0  # Maximum order value in quote currency
    grid_spacing_atr_mult: float = 0.5  # Grid spacing as multiple of ATR
    enable_post_only: bool = True  # Force maker orders
    order_timeout_seconds: int = 300  # Cancel unfilled orders after this time
    rebalance_threshold: float = 0.1  # Rebalance when inventory skew > 10%
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'OrderConfig':
        """Create config from dictionary"""
        return cls(**{k: v for k, v in config.items() if k in cls.__annotations__})


class OrderManager:
    """
    Manages order placement, sizing, and grid levels
    Ensures maker-only execution and handles edge cases
    """
    
    def __init__(self, config: Dict[str, Any], spread_manager: Optional['SpreadManager'] = None):
        """
        Initialize order manager
        
        Args:
            config: Order configuration dictionary
            spread_manager: Optional spread manager for price calculations
        """
        self.config = OrderConfig.from_dict(config)
        self.spread_manager = spread_manager
        self.active_grids: Dict[str, List[GridLevel]] = {}
        self.pending_orders: Dict[str, Dict] = {}
        self.filled_orders: List[Dict] = []
        self._order_counter = 0
        
    def build_grid(
        self, 
        pair: str, 
        current_price: float,
        atr: float,
        account_balance: float,
        best_bid: float,
        best_ask: float,
        existing_position: float = 0
    ) -> List[GridLevel]:
        """
        Build grid levels for trading
        
        Args:
            pair: Trading pair
            current_price: Current market price
            atr: Average True Range
            account_balance: Available account balance
            best_bid: Current best bid
            best_ask: Current best ask
            existing_position: Existing position size
            
        Returns:
            List of grid levels to place
        """
        grids = []
        
        # Calculate grid spacing
        grid_spacing = self._calculate_grid_spacing(atr, current_price)
        
        # Calculate base order size
        base_size = self._calculate_base_order_size(account_balance, current_price)
        
        # Determine if we need to rebalance
        needs_rebalance = self._check_rebalance_needed(existing_position, account_balance)
        
        for level in range(self.config.max_grid_levels):
            # Buy side grid
            buy_price = self._calculate_grid_price(
                best_bid, grid_spacing, level, OrderSide.BUY
            )
            buy_size = self._calculate_grid_size(base_size, level)
            
            # Validate order parameters
            if self._validate_order(buy_price, buy_size, current_price):
                grids.append(GridLevel(
                    level=level,
                    price=buy_price,
                    quantity=buy_size,
                    side=OrderSide.BUY,
                    distance_from_mid=abs(current_price - buy_price) / current_price
                ))
            
            # Sell side grid (only if we have position or not rebalancing)
            if existing_position > 0 or not needs_rebalance:
                sell_price = self._calculate_grid_price(
                    best_ask, grid_spacing, level, OrderSide.SELL
                )
                sell_size = self._calculate_grid_size(base_size, level)
                
                if self._validate_order(sell_price, sell_size, current_price):
                    grids.append(GridLevel(
                        level=level,
                        price=sell_price,
                        quantity=sell_size,
                        side=OrderSide.SELL,
                        distance_from_mid=abs(current_price - sell_price) / current_price
                    ))
        
        # Store active grids
        self.active_grids[pair] = grids
        
        logger.info(f"Built {len(grids)} grid levels for {pair}")
        return grids
    
    def _calculate_grid_spacing(self, atr: float, current_price: float) -> float:
        """
        Calculate dynamic grid spacing based on ATR
        
        Args:
            atr: Average True Range
            current_price: Current market price
            
        Returns:
            Grid spacing amount
        """
        # Base spacing on ATR but ensure minimum spread
        spacing = atr * self.config.grid_spacing_atr_mult
        min_spacing = current_price * 0.001  # Minimum 0.1% spacing
        
        return max(spacing, min_spacing)
    
    def _calculate_grid_price(
        self, 
        reference_price: float,
        spacing: float,
        level: int,
        side: OrderSide
    ) -> float:
        """
        Calculate price for a specific grid level
        
        Args:
            reference_price: Reference price (bid or ask)
            spacing: Grid spacing amount
            level: Grid level (0, 1, 2, ...)
            side: Order side
            
        Returns:
            Grid price
        """
        if side == OrderSide.BUY:
            # Place buy orders below bid
            price = reference_price - (spacing * (level + 1))
        else:
            # Place sell orders above ask
            price = reference_price + (spacing * (level + 1))
        
        # Ensure price is positive
        return max(price, 0.********)
    
    def _calculate_base_order_size(self, account_balance: float, current_price: float) -> float:
        """
        Calculate base order size
        
        Args:
            account_balance: Available account balance
            current_price: Current market price
            
        Returns:
            Base order size in base currency
        """
        # Calculate value in quote currency
        order_value = account_balance * self.config.base_order_size_pct
        
        # Apply min/max constraints
        order_value = max(self.config.min_order_value, 
                         min(order_value, self.config.max_order_value))
        
        # Convert to base currency
        return order_value / current_price
    
    def _calculate_grid_size(self, base_size: float, level: int) -> float:
        """
        Calculate order size for a specific grid level
        
        Args:
            base_size: Base order size
            level: Grid level
            
        Returns:
            Order size for the level
        """
        # Increase size for further levels (martingale-style)
        return base_size * (self.config.order_size_multiplier ** level)
    
    def _validate_order(self, price: float, size: float, current_price: float) -> bool:
        """
        Validate order parameters
        
        Args:
            price: Order price
            size: Order size
            current_price: Current market price
            
        Returns:
            True if order is valid
        """
        # Check minimum order value
        order_value = price * size
        if order_value < self.config.min_order_value:
            logger.debug(f"Order value {order_value} below minimum {self.config.min_order_value}")
            return False
        
        # Check maximum order value
        if order_value > self.config.max_order_value:
            logger.debug(f"Order value {order_value} above maximum {self.config.max_order_value}")
            return False
        
        # Check price sanity (not too far from current price)
        price_deviation = abs(price - current_price) / current_price
        if price_deviation > 0.1:  # More than 10% from current price
            logger.warning(f"Price {price} deviates {price_deviation:.2%} from current {current_price}")
            return False
        
        return True
    
    def _check_rebalance_needed(self, position: float, balance: float) -> bool:
        """
        Check if position rebalancing is needed
        
        Args:
            position: Current position value
            balance: Account balance
            
        Returns:
            True if rebalancing needed
        """
        if balance == 0:
            return False
        
        position_ratio = abs(position) / balance
        return position_ratio > self.config.rebalance_threshold
    
    def update_order_status(self, order_id: str, status: str, filled_qty: float = 0) -> None:
        """
        Update order status
        
        Args:
            order_id: Order ID
            status: New status
            filled_qty: Filled quantity if applicable
        """
        if order_id in self.pending_orders:
            self.pending_orders[order_id]['status'] = status
            self.pending_orders[order_id]['filled_qty'] = filled_qty
            
            if status == 'filled':
                self.filled_orders.append(self.pending_orders[order_id])
                del self.pending_orders[order_id]
                logger.info(f"Order {order_id} filled")
            elif status == 'cancelled':
                del self.pending_orders[order_id]
                logger.info(f"Order {order_id} cancelled")
    
    def cancel_stale_orders(self, current_time: datetime) -> List[str]:
        """
        Cancel orders that have been pending too long
        
        Args:
            current_time: Current timestamp
            
        Returns:
            List of order IDs to cancel
        """
        to_cancel = []
        timeout_delta = timedelta(seconds=self.config.order_timeout_seconds)
        
        for order_id, order in self.pending_orders.items():
            if current_time - order['placed_at'] > timeout_delta:
                to_cancel.append(order_id)
                logger.info(f"Marking order {order_id} for cancellation (timeout)")
        
        return to_cancel
    
    def get_active_orders_count(self, pair: Optional[str] = None) -> int:
        """
        Get count of active orders
        
        Args:
            pair: Optional pair to filter by
            
        Returns:
            Number of active orders
        """
        if pair:
            return sum(1 for o in self.pending_orders.values() if o.get('pair') == pair)
        return len(self.pending_orders)
    
    def reset_grids(self, pair: Optional[str] = None) -> None:
        """
        Reset grid levels
        
        Args:
            pair: Optional pair to reset, or all if None
        """
        if pair:
            self.active_grids.pop(pair, None)
            logger.info(f"Reset grids for {pair}")
        else:
            self.active_grids.clear()
            logger.info("Reset all grids")
    
    def get_grid_statistics(self, pair: str) -> Dict[str, Any]:
        """
        Get statistics for active grids
        
        Args:
            pair: Trading pair
            
        Returns:
            Grid statistics dictionary
        """
        if pair not in self.active_grids:
            return {}
        
        grids = self.active_grids[pair]
        buy_grids = [g for g in grids if g.side == OrderSide.BUY]
        sell_grids = [g for g in grids if g.side == OrderSide.SELL]
        
        return {
            'total_levels': len(grids),
            'buy_levels': len(buy_grids),
            'sell_levels': len(sell_grids),
            'active_orders': sum(1 for g in grids if g.is_active),
            'avg_distance_from_mid': np.mean([g.distance_from_mid for g in grids]) if grids else 0,
            'total_value': sum(g.price * g.quantity for g in grids)
        }
