"""
Market Detector Component
Analyzes market conditions and determines if they're suitable for trading
"""

from typing import Dict, Optional, Any, List, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import numpy as np
import pandas as pd
from pandas import Series, DataFrame
import talib

logger = logging.getLogger(__name__)


class MarketState(Enum):
    """Market state enumeration"""
    SIDEWAYS = "sideways"
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"


@dataclass
class MarketConfig:
    """Market detector configuration"""
    atr_window: int = 14  # ATR calculation window
    adx_window: int = 14  # ADX calculation window
    adx_threshold: float = 25.0  # ADX threshold for trend detection
    bb_window: int = 20  # Bollinger Bands window
    bb_std: float = 2.0  # Bollinger Bands standard deviation
    rsi_window: int = 14  # RSI calculation window
    rsi_oversold: float = 30.0  # RSI oversold threshold
    rsi_overbought: float = 70.0  # RSI overbought threshold
    volume_ma_window: int = 20  # Volume moving average window
    volatility_threshold: float = 0.03  # 3% volatility threshold
    sideways_confirmation_bars: int = 5  # Bars to confirm sideways market
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'MarketConfig':
        """Create config from dictionary"""
        return cls(**{k: v for k, v in config.items() if k in cls.__annotations__})


class MarketDetector:
    """
    Detects market conditions and determines trading suitability
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize market detector
        
        Args:
            config: Market configuration dictionary
        """
        self.config = MarketConfig.from_dict(config)
        self.current_state = MarketState.UNKNOWN
        self.state_history: List[MarketState] = []
        self.confidence_scores: Dict[MarketState, float] = {}
        
    def compute_indicators(self, dataframe: DataFrame) -> DataFrame:
        """
        Add market analysis indicators to dataframe
        
        Args:
            dataframe: OHLCV dataframe
            
        Returns:
            DataFrame with indicators added
        """
        # Price-based indicators
        dataframe['atr'] = talib.ATR(
            dataframe['high'],
            dataframe['low'],
            dataframe['close'],
            timeperiod=self.config.atr_window
        )
        
        dataframe['adx'] = talib.ADX(
            dataframe['high'],
            dataframe['low'],
            dataframe['close'],
            timeperiod=self.config.adx_window
        )
        
        dataframe['plus_di'] = talib.PLUS_DI(
            dataframe['high'],
            dataframe['low'],
            dataframe['close'],
            timeperiod=self.config.adx_window
        )
        
        dataframe['minus_di'] = talib.MINUS_DI(
            dataframe['high'],
            dataframe['low'],
            dataframe['close'],
            timeperiod=self.config.adx_window
        )
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(
            dataframe['close'],
            timeperiod=self.config.bb_window,
            nbdevup=self.config.bb_std,
            nbdevdn=self.config.bb_std,
            matype=0
        )
        dataframe['bb_upper'] = bb_upper
        dataframe['bb_middle'] = bb_middle
        dataframe['bb_lower'] = bb_lower
        dataframe['bb_width'] = (bb_upper - bb_lower) / bb_middle
        dataframe['bb_position'] = (dataframe['close'] - bb_lower) / (bb_upper - bb_lower)
        
        # RSI
        dataframe['rsi'] = talib.RSI(
            dataframe['close'],
            timeperiod=self.config.rsi_window
        )
        
        # Volume indicators
        dataframe['volume_ma'] = dataframe['volume'].rolling(
            window=self.config.volume_ma_window
        ).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_ma']
        
        # Volatility measures
        dataframe['returns'] = dataframe['close'].pct_change()
        dataframe['volatility'] = dataframe['returns'].rolling(
            window=self.config.atr_window
        ).std()
        
        # Price action
        dataframe['high_low_ratio'] = (dataframe['high'] - dataframe['low']) / dataframe['close']
        dataframe['close_position'] = (dataframe['close'] - dataframe['low']) / (
            dataframe['high'] - dataframe['low']
        )
        
        # Market state detection
        dataframe['market_state'] = dataframe.apply(
            lambda row: self._detect_state(row).value,
            axis=1
        )
        
        # Sideways market confirmation
        dataframe['is_sideways'] = dataframe.apply(
            lambda row: self._is_sideways_confirmed(row, dataframe),
            axis=1
        )
        
        return dataframe
    
    def _detect_state(self, row: Series) -> MarketState:
        """
        Detect market state from indicators
        
        Args:
            row: DataFrame row with indicators
            
        Returns:
            Detected market state
        """
        # Check for missing data
        if pd.isna(row['adx']) or pd.isna(row['atr']):
            return MarketState.UNKNOWN
        
        # Initialize confidence scores
        scores = {
            MarketState.SIDEWAYS: 0.0,
            MarketState.TRENDING_UP: 0.0,
            MarketState.TRENDING_DOWN: 0.0,
            MarketState.VOLATILE: 0.0
        }
        
        # ADX-based trend detection
        if row['adx'] < self.config.adx_threshold:
            scores[MarketState.SIDEWAYS] += 0.3
        else:
            if row['plus_di'] > row['minus_di']:
                scores[MarketState.TRENDING_UP] += 0.3
            else:
                scores[MarketState.TRENDING_DOWN] += 0.3
        
        # Bollinger Bands position
        if 0.3 < row['bb_position'] < 0.7:
            scores[MarketState.SIDEWAYS] += 0.2
        elif row['bb_position'] > 0.8:
            scores[MarketState.TRENDING_UP] += 0.2
        elif row['bb_position'] < 0.2:
            scores[MarketState.TRENDING_DOWN] += 0.2
        
        # RSI analysis
        if self.config.rsi_oversold < row['rsi'] < self.config.rsi_overbought:
            scores[MarketState.SIDEWAYS] += 0.2
        elif row['rsi'] > self.config.rsi_overbought:
            scores[MarketState.TRENDING_UP] += 0.1
            scores[MarketState.VOLATILE] += 0.1
        elif row['rsi'] < self.config.rsi_oversold:
            scores[MarketState.TRENDING_DOWN] += 0.1
            scores[MarketState.VOLATILE] += 0.1
        
        # Volatility check
        if row['volatility'] > self.config.volatility_threshold:
            scores[MarketState.VOLATILE] += 0.3
        else:
            scores[MarketState.SIDEWAYS] += 0.1
        
        # Bollinger Band width (volatility indicator)
        if row['bb_width'] < 0.05:  # Tight bands = low volatility
            scores[MarketState.SIDEWAYS] += 0.2
        elif row['bb_width'] > 0.15:  # Wide bands = high volatility
            scores[MarketState.VOLATILE] += 0.2
        
        # Store confidence scores
        self.confidence_scores = scores
        
        # Return state with highest score
        state = max(scores, key=scores.get)
        
        # Store in history
        self.state_history.append(state)
        if len(self.state_history) > 100:
            self.state_history.pop(0)
        
        self.current_state = state
        return state
    
    def _is_sideways_confirmed(self, row: Series, dataframe: DataFrame) -> bool:
        """
        Confirm sideways market with multiple bars
        
        Args:
            row: Current row
            dataframe: Full dataframe
            
        Returns:
            True if sideways market is confirmed
        """
        # Get current index
        try:
            idx = dataframe.index.get_loc(row.name)
        except KeyError:
            return False
        
        # Need enough history
        if idx < self.config.sideways_confirmation_bars:
            return False
        
        # Check last N bars
        start_idx = max(0, idx - self.config.sideways_confirmation_bars)
        recent_states = dataframe.iloc[start_idx:idx + 1]['market_state'].values
        
        # Count sideways occurrences
        sideways_count = sum(1 for state in recent_states if state == MarketState.SIDEWAYS.value)
        
        # Require majority to be sideways
        return sideways_count >= (self.config.sideways_confirmation_bars * 0.6)
    
    def is_suitable_for_trading(self, row: Series) -> bool:
        """
        Determine if current market conditions are suitable for grid trading
        
        Args:
            row: Current market data row
            
        Returns:
            True if conditions are suitable
        """
        # Must be in sideways market
        if not row.get('is_sideways', False):
            logger.debug("Market not sideways, skipping trade")
            return False
        
        # Check volatility is not too high
        if row.get('volatility', 0) > self.config.volatility_threshold:
            logger.debug(f"Volatility {row['volatility']:.4f} exceeds threshold")
            return False
        
        # Check volume is sufficient
        if row.get('volume_ratio', 0) < 0.5:
            logger.debug("Volume too low compared to average")
            return False
        
        # Check ADX confirms no strong trend
        if row.get('adx', 0) > self.config.adx_threshold * 1.2:
            logger.debug(f"ADX {row['adx']:.2f} indicates trending market")
            return False
        
        return True
    
    def get_market_confidence(self) -> Dict[str, float]:
        """
        Get confidence scores for each market state
        
        Returns:
            Dictionary of market states and confidence scores
        """
        return {
            state.value: score 
            for state, score in self.confidence_scores.items()
        }
    
    def get_state_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about market states
        
        Returns:
            Dictionary of state statistics
        """
        if not self.state_history:
            return {}
        
        state_counts = {}
        for state in MarketState:
            count = sum(1 for s in self.state_history if s == state)
            state_counts[state.value] = count
        
        total = len(self.state_history)
        state_percentages = {
            state: (count / total * 100) if total > 0 else 0
            for state, count in state_counts.items()
        }
        
        return {
            'current_state': self.current_state.value,
            'state_counts': state_counts,
            'state_percentages': state_percentages,
            'history_length': total,
            'confidence_scores': self.get_market_confidence()
        }
    
    def should_exit_positions(self, row: Series) -> bool:
        """
        Determine if market conditions warrant exiting all positions
        
        Args:
            row: Current market data row
            
        Returns:
            True if positions should be exited
        """
        # Exit if strong trend develops
        if row.get('adx', 0) > self.config.adx_threshold * 1.5:
            logger.warning(f"Strong trend detected (ADX: {row['adx']:.2f}), consider exit")
            return True
        
        # Exit if volatility spikes
        if row.get('volatility', 0) > self.config.volatility_threshold * 2:
            logger.warning(f"High volatility detected ({row['volatility']:.4f}), consider exit")
            return True
        
        # Exit if price breaks out of Bollinger Bands significantly
        if row.get('bb_position', 0.5) > 1.1 or row.get('bb_position', 0.5) < -0.1:
            logger.warning("Price breakout detected, consider exit")
            return True
        
        return False
    
    def get_trading_intensity(self, row: Series) -> float:
        """
        Calculate trading intensity based on market conditions
        Returns a multiplier for position sizing (0.0 to 1.0)
        
        Args:
            row: Current market data row
            
        Returns:
            Trading intensity multiplier
        """
        if not self.is_suitable_for_trading(row):
            return 0.0
        
        intensity = 1.0
        
        # Reduce intensity if ADX is rising (trend forming)
        if row.get('adx', 0) > self.config.adx_threshold * 0.8:
            intensity *= 0.7
        
        # Reduce intensity if volatility is elevated
        volatility_ratio = row.get('volatility', 0) / self.config.volatility_threshold
        if volatility_ratio > 0.5:
            intensity *= (1.0 - min(volatility_ratio * 0.5, 0.5))
        
        # Increase intensity if firmly in middle of Bollinger Bands
        bb_position = row.get('bb_position', 0.5)
        if 0.4 < bb_position < 0.6:
            intensity *= 1.1
        
        # Adjust based on RSI
        rsi = row.get('rsi', 50)
        if 45 < rsi < 55:
            intensity *= 1.05  # Neutral RSI is good for grid trading
        
        return min(max(intensity, 0.0), 1.0)
