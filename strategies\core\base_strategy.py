"""
Base Strategy Abstract Class
Provides foundation for all trading strategies with component injection
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import logging
from dataclasses import dataclass
import pandas as pd
from freqtrade.strategy import IStrategy
from freqtrade.persistence import Trade

logger = logging.getLogger(__name__)


@dataclass
class StrategyConfig:
    """Configuration container for strategy components"""
    market_config: Dict[str, Any]
    spread_config: Dict[str, Any]
    risk_config: Dict[str, Any]
    metrics_config: Dict[str, Any]
    order_config: Dict[str, Any]
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'StrategyConfig':
        """Factory method to create config from dictionary"""
        return cls(
            market_config=config.get('market', {}),
            spread_config=config.get('spread', {}),
            risk_config=config.get('risk', {}),
            metrics_config=config.get('metrics', {}),
            order_config=config.get('order', {})
        )


class BaseStrategy(IStrategy, ABC):
    """
    Abstract base class for component-based trading strategies
    Implements template method pattern for strategy execution
    """
    
    # Interface version for Freqtrade compatibility
    INTERFACE_VERSION = 3
    
    # Default configuration
    minimal_roi = {"0": 100}  # Disable ROI, we manage exits ourselves
    stoploss = -0.99  # We implement custom stop loss
    trailing_stop = False
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = True
    
    # Timeframe
    timeframe = '1m'
    
    # Enable position adjustment
    position_adjustment_enable = True
    max_entry_position_adjustment = 3
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize strategy with dependency injection
        
        Args:
            config: Strategy configuration dictionary
        """
        super().__init__(config)
        self.strategy_config = StrategyConfig.from_dict(config)
        self._initialize_components()
        self._setup_logging()
        
    @abstractmethod
    def _initialize_components(self) -> None:
        """Initialize strategy components - must be implemented by subclasses"""
        pass
    
    def _setup_logging(self) -> None:
        """Configure logging for the strategy"""
        import colorlog
        
        handler = colorlog.StreamHandler()
        handler.setFormatter(
            colorlog.ColoredFormatter(
                '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                log_colors={
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                }
            )
        )
        
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    @abstractmethod
    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Add technical indicators to the dataframe
        
        Args:
            dataframe: OHLCV data
            metadata: Pair metadata
            
        Returns:
            DataFrame with indicators
        """
        pass
    
    @abstractmethod
    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Define entry signals
        
        Args:
            dataframe: DataFrame with indicators
            metadata: Pair metadata
            
        Returns:
            DataFrame with entry signals
        """
        pass
    
    @abstractmethod
    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Define exit signals
        
        Args:
            dataframe: DataFrame with indicators
            metadata: Pair metadata
            
        Returns:
            DataFrame with exit signals
        """
        pass
    
    def confirm_trade_entry(
        self, 
        pair: str, 
        order_type: str, 
        amount: float,
        rate: float, 
        time_in_force: str, 
        current_time: datetime,
        entry_tag: Optional[str], 
        side: str,
        **kwargs
    ) -> bool:
        """
        Final check before placing an order
        
        Returns:
            True if trade should be placed, False otherwise
        """
        # Log trade attempt
        logger.info(
            f"Trade entry confirmation: {pair} | {side} | "
            f"Amount: {amount} | Rate: {rate} | Tag: {entry_tag}"
        )
        
        # Perform final validation
        return self._validate_trade_entry(pair, amount, rate, side)
    
    @abstractmethod
    def _validate_trade_entry(
        self, 
        pair: str, 
        amount: float, 
        rate: float, 
        side: str
    ) -> bool:
        """Validate trade entry - must be implemented by subclasses"""
        pass
    
    def custom_exit(
        self, 
        pair: str, 
        trade: Trade,
        current_time: datetime, 
        current_rate: float,
        current_profit: float, 
        **kwargs
    ) -> Optional[Union[str, Tuple[str, str]]]:
        """
        Custom exit logic
        
        Returns:
            Exit reason string or None
        """
        return self._evaluate_exit_conditions(trade, current_rate, current_profit)
    
    @abstractmethod
    def _evaluate_exit_conditions(
        self, 
        trade: Trade, 
        current_rate: float, 
        current_profit: float
    ) -> Optional[str]:
        """Evaluate exit conditions - must be implemented by subclasses"""
        pass
    
    def adjust_trade_position(
        self,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        min_stake: Optional[float],
        max_stake: float,
        **kwargs
    ) -> Optional[float]:
        """
        Position adjustment for grid trading
        
        Returns:
            Additional amount to buy/sell or None
        """
        return self._calculate_position_adjustment(
            trade, current_rate, current_profit, min_stake, max_stake
        )
    
    @abstractmethod
    def _calculate_position_adjustment(
        self,
        trade: Trade,
        current_rate: float,
        current_profit: float,
        min_stake: Optional[float],
        max_stake: float
    ) -> Optional[float]:
        """Calculate position adjustment - must be implemented by subclasses"""
        pass
    
    def informative_pairs(self) -> List[Tuple[str, str]]:
        """
        Define additional pairs for information
        
        Returns:
            List of (pair, timeframe) tuples
        """
        return []
    
    def leverage(
        self, 
        pair: str, 
        current_time: datetime, 
        current_rate: float,
        proposed_leverage: float, 
        max_leverage: float, 
        side: str,
        **kwargs
    ) -> float:
        """
        Leverage calculation - we use spot trading only
        
        Returns:
            Always 1.0 for spot trading
        """
        return 1.0
