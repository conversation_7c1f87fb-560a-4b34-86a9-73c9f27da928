"""
Comprehensive Unit Tests for Grid Maker Strategy Components
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategies.core.order_manager import OrderManager, OrderSide, GridLevel
from strategies.core.spread_manager import SpreadManager
from strategies.core.market_detector import MarketDetector, MarketState
from strategies.core.risk_manager import RiskManager, RiskLevel
from strategies.db.sqlite_logger import SQLiteLogger
from strategies.db.metrics import MetricsTracker


# Test fixtures
@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        'market': {
            'atr_window': 14,
            'adx_window': 14,
            'adx_threshold': 25.0,
            'bb_window': 20,
            'bb_std': 2.0,
            'rsi_window': 14,
            'rsi_oversold': 30.0,
            'rsi_overbought': 70.0,
            'volume_ma_window': 20,
            'volatility_threshold': 0.03,
            'sideways_confirmation_bars': 5
        },
        'spread': {
            'min_spread_bps': 10.0,
            'max_spread_bps': 50.0,
            'atr_multiplier': 0.5,
            'volatility_adjustment': True,
            'liquidity_adjustment': True,
            'maker_fee': 0.0,
            'taker_fee': 0.001,
            'safety_margin_bps': 2.0
        },
        'risk': {
            'max_position_pct': 0.15,
            'max_total_exposure_pct': 0.5,
            'stop_loss_atr_mult': 3.0,
            'take_profit_atr_mult': 5.0,
            'max_drawdown_pct': 0.10,
            'max_daily_loss_pct': 0.05,
            'max_consecutive_losses': 5,
            'min_risk_reward_ratio': 1.5
        },
        'order': {
            'max_grid_levels': 3,
            'base_order_size_pct': 0.02,
            'order_size_multiplier': 1.5,
            'min_order_value': 10.0,
            'max_order_value': 10000.0,
            'grid_spacing_atr_mult': 0.5,
            'enable_post_only': True,
            'order_timeout_seconds': 300,
            'rebalance_threshold': 0.1
        },
        'metrics': {
            'enable_prometheus': False,
            'metrics_window': 1000,
            'update_interval': 60
        }
    }


@pytest.fixture
def sample_dataframe():
    """Sample OHLCV dataframe for testing"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    data = {
        'open': np.random.uniform(0.5, 0.6, 100),
        'high': np.random.uniform(0.55, 0.65, 100),
        'low': np.random.uniform(0.45, 0.55, 100),
        'close': np.random.uniform(0.5, 0.6, 100),
        'volume': np.random.uniform(1000, 10000, 100)
    }
    df = pd.DataFrame(data, index=dates)
    # Ensure high > low
    df['high'] = df[['open', 'high', 'close']].max(axis=1)
    df['low'] = df[['open', 'low', 'close']].min(axis=1)
    return df


# OrderManager Tests
class TestOrderManager:
    """Test OrderManager component"""
    
    def test_initialization(self, sample_config):
        """Test OrderManager initialization"""
        manager = OrderManager(sample_config['order'])
        assert manager.config.max_grid_levels == 3
        assert manager.config.base_order_size_pct == 0.02
        assert len(manager.active_grids) == 0
        assert len(manager.pending_orders) == 0
    
    def test_build_grid(self, sample_config):
        """Test grid building logic"""
        manager = OrderManager(sample_config['order'])
        
        grids = manager.build_grid(
            pair='XRP/USDT',
            current_price=0.5,
            atr=0.01,
            account_balance=10000,
            best_bid=0.499,
            best_ask=0.501,
            existing_position=0
        )
        
        assert len(grids) > 0
        assert len(grids) <= manager.config.max_grid_levels * 2  # Buy and sell sides
        
        # Check grid levels are properly spaced
        buy_grids = [g for g in grids if g.side == OrderSide.BUY]
        sell_grids = [g for g in grids if g.side == OrderSide.SELL]
        
        # Buy prices should be below bid
        for grid in buy_grids:
            assert grid.price < 0.499
        
        # Sell prices should be above ask
        for grid in sell_grids:
            assert grid.price > 0.501
    
    def test_grid_spacing(self, sample_config):
        """Test grid spacing calculation"""
        manager = OrderManager(sample_config['order'])
        
        atr = 0.01
        current_price = 0.5
        spacing = manager._calculate_grid_spacing(atr, current_price)
        
        assert spacing > 0
        assert spacing >= current_price * 0.001  # Minimum 0.1% spacing
    
    def test_order_validation(self, sample_config):
        """Test order validation logic"""
        manager = OrderManager(sample_config['order'])
        
        # Valid order
        assert manager._validate_order(
            price=0.5,
            size=100,
            current_price=0.5
        ) == True
        
        # Order too small
        assert manager._validate_order(
            price=0.5,
            size=1,  # $0.5 value, below minimum
            current_price=0.5
        ) == False
        
        # Order too large
        assert manager._validate_order(
            price=0.5,
            size=30000,  # $15000 value, above maximum
            current_price=0.5
        ) == False
    
    def test_stale_order_cancellation(self, sample_config):
        """Test stale order cancellation"""
        manager = OrderManager(sample_config['order'])
        
        # Add some test orders
        old_time = datetime.now() - timedelta(seconds=400)
        recent_time = datetime.now() - timedelta(seconds=100)
        
        manager.pending_orders['order1'] = {'placed_at': old_time}
        manager.pending_orders['order2'] = {'placed_at': recent_time}
        
        to_cancel = manager.cancel_stale_orders(datetime.now())
        
        assert 'order1' in to_cancel
        assert 'order2' not in to_cancel


# SpreadManager Tests
class TestSpreadManager:
    """Test SpreadManager component"""
    
    def test_initialization(self, sample_config):
        """Test SpreadManager initialization"""
        manager = SpreadManager(sample_config['spread'])
        assert manager.config.min_spread_bps == 10.0
        assert manager.config.max_spread_bps == 50.0
        assert manager._last_optimal_spread is None
    
    def test_optimal_spread_calculation(self, sample_config):
        """Test optimal spread calculation"""
        manager = SpreadManager(sample_config['spread'])
        
        bid_spread, ask_spread = manager.calculate_optimal_spread(
            current_price=0.5,
            best_bid=0.499,
            best_ask=0.501,
            atr=0.01,
            volume_24h=1000000,
            orderbook_depth={'bids_value': 50000, 'asks_value': 50000}
        )
        
        assert bid_spread > 0
        assert ask_spread > 0
        assert bid_spread + ask_spread > (manager.config.min_spread_bps / 10000)
    
    def test_spread_validation(self, sample_config):
        """Test spread validation for maker orders"""
        manager = SpreadManager(sample_config['spread'])
        
        # Valid spread (maker orders)
        assert manager.validate_spread(
            bid_price=0.498,
            ask_price=0.502,
            best_bid=0.499,
            best_ask=0.501
        ) == True
        
        # Invalid spread (would cross)
        assert manager.validate_spread(
            bid_price=0.500,  # Above best bid
            ask_price=0.502,
            best_bid=0.499,
            best_ask=0.501
        ) == False
    
    def test_price_adjustment_for_maker(self, sample_config):
        """Test price adjustment to ensure maker execution"""
        manager = SpreadManager(sample_config['spread'])
        
        # Buy order that would cross spread
        adjusted = manager.adjust_price_for_maker(
            price=0.500,
            side='buy',
            best_bid=0.499,
            best_ask=0.501
        )
        assert adjusted < 0.499  # Should be below best bid
        
        # Sell order that would cross spread
        adjusted = manager.adjust_price_for_maker(
            price=0.500,
            side='sell',
            best_bid=0.499,
            best_ask=0.501
        )
        assert adjusted > 0.501  # Should be above best ask


# MarketDetector Tests
class TestMarketDetector:
    """Test MarketDetector component"""
    
    def test_initialization(self, sample_config):
        """Test MarketDetector initialization"""
        detector = MarketDetector(sample_config['market'])
        assert detector.config.adx_threshold == 25.0
        assert detector.current_state == MarketState.UNKNOWN
        assert len(detector.state_history) == 0
    
    @patch('talib.ATR')
    @patch('talib.ADX')
    @patch('talib.RSI')
    @patch('talib.BBANDS')
    @patch('talib.PLUS_DI')
    @patch('talib.MINUS_DI')
    def test_compute_indicators(self, mock_minus_di, mock_plus_di, mock_bbands, 
                               mock_rsi, mock_adx, mock_atr, sample_config, sample_dataframe):
        """Test indicator computation"""
        detector = MarketDetector(sample_config['market'])
        
        # Mock indicator returns
        mock_atr.return_value = pd.Series(np.full(100, 0.01))
        mock_adx.return_value = pd.Series(np.full(100, 20.0))
        mock_rsi.return_value = pd.Series(np.full(100, 50.0))
        mock_plus_di.return_value = pd.Series(np.full(100, 25.0))
        mock_minus_di.return_value = pd.Series(np.full(100, 20.0))
        
        # Mock Bollinger Bands
        upper = pd.Series(np.full(100, 0.52))
        middle = pd.Series(np.full(100, 0.50))
        lower = pd.Series(np.full(100, 0.48))
        mock_bbands.return_value = (upper, middle, lower)
        
        result = detector.compute_indicators(sample_dataframe)
        
        assert 'atr' in result.columns
        assert 'adx' in result.columns
        assert 'rsi' in result.columns
        assert 'market_state' in result.columns
        assert 'is_sideways' in result.columns
    
    def test_market_state_detection(self, sample_config):
        """Test market state detection logic"""
        detector = MarketDetector(sample_config['market'])
        
        # Create mock row for sideways market
        sideways_row = pd.Series({
            'adx': 15.0,  # Below threshold
            'atr': 0.01,
            'plus_di': 20.0,
            'minus_di': 18.0,
            'bb_position': 0.5,  # Middle of bands
            'rsi': 50.0,  # Neutral
            'volatility': 0.01,  # Low volatility
            'bb_width': 0.04  # Normal width
        })
        
        state = detector._detect_state(sideways_row)
        assert state == MarketState.SIDEWAYS
        
        # Create mock row for trending market
        trending_row = pd.Series({
            'adx': 35.0,  # Above threshold
            'atr': 0.02,
            'plus_di': 30.0,
            'minus_di': 15.0,
            'bb_position': 0.9,  # Near upper band
            'rsi': 75.0,  # Overbought
            'volatility': 0.02,
            'bb_width': 0.06
        })
        
        state = detector._detect_state(trending_row)
        assert state in [MarketState.TRENDING_UP, MarketState.TRENDING_DOWN]
    
    def test_trading_suitability(self, sample_config):
        """Test trading suitability determination"""
        detector = MarketDetector(sample_config['market'])
        
        # Suitable conditions
        suitable_row = pd.Series({
            'is_sideways': True,
            'volatility': 0.02,
            'volume_ratio': 1.0,
            'adx': 20.0
        })
        assert detector.is_suitable_for_trading(suitable_row) == True
        
        # Unsuitable conditions (high volatility)
        unsuitable_row = pd.Series({
            'is_sideways': True,
            'volatility': 0.05,
            'volume_ratio': 1.0,
            'adx': 20.0
        })
        assert detector.is_suitable_for_trading(unsuitable_row) == False


# RiskManager Tests
class TestRiskManager:
    """Test RiskManager component"""
    
    def test_initialization(self, sample_config):
        """Test RiskManager initialization"""
        manager = RiskManager(sample_config['risk'])
        assert manager.config.max_position_pct == 0.15
        assert manager.config.max_total_exposure_pct == 0.5
        assert manager.consecutive_losses == 0
        assert len(manager.positions) == 0
    
    def test_entry_risk_evaluation(self, sample_config):
        """Test entry risk evaluation"""
        manager = RiskManager(sample_config['risk'])
        
        # Valid entry
        can_enter, reason = manager.evaluate_entry_risk(
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=200,  # $100 position
            account_balance=10000,
            atr=0.01,
            existing_positions={}
        )
        assert can_enter == True
        
        # Position too large
        can_enter, reason = manager.evaluate_entry_risk(
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=4000,  # $2000 position (20% of account)
            account_balance=10000,
            atr=0.01,
            existing_positions={}
        )
        assert can_enter == False
        assert 'Position size' in reason
    
    def test_stop_loss_calculation(self, sample_config):
        """Test stop loss calculation"""
        manager = RiskManager(sample_config['risk'])
        
        # Long position stop loss
        stop_loss = manager.calculate_stop_loss(
            entry_price=0.5,
            side='buy',
            atr=0.01
        )
        assert stop_loss < 0.5
        assert stop_loss == 0.5 - (0.01 * manager.config.stop_loss_atr_mult)
        
        # Short position stop loss
        stop_loss = manager.calculate_stop_loss(
            entry_price=0.5,
            side='sell',
            atr=0.01
        )
        assert stop_loss > 0.5
        assert stop_loss == 0.5 + (0.01 * manager.config.stop_loss_atr_mult)
    
    def test_consecutive_losses_tracking(self, sample_config):
        """Test consecutive losses tracking"""
        manager = RiskManager(sample_config['risk'])
        
        # Set up some consecutive losses
        manager.consecutive_losses = 4
        
        # Should allow trading with 4 losses
        can_enter, _ = manager.evaluate_entry_risk(
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=100,
            account_balance=10000,
            atr=0.01,
            existing_positions={}
        )
        assert can_enter == True
        
        # Should block with 5 losses
        manager.consecutive_losses = 5
        can_enter, reason = manager.evaluate_entry_risk(
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=100,
            account_balance=10000,
            atr=0.01,
            existing_positions={}
        )
        assert can_enter == False
        assert 'consecutive losses' in reason.lower()
    
    def test_portfolio_risk_level(self, sample_config):
        """Test portfolio risk level determination"""
        manager = RiskManager(sample_config['risk'])
        
        # Low risk (no positions)
        assert manager._get_portfolio_risk_level() == RiskLevel.LOW
        
        # Add high risk conditions
        manager.consecutive_losses = 4
        manager.max_drawdown = -0.08
        assert manager._get_portfolio_risk_level() in [RiskLevel.HIGH, RiskLevel.CRITICAL]


# SQLiteLogger Tests
class TestSQLiteLogger:
    """Test SQLiteLogger component"""
    
    @pytest.fixture
    def temp_db(self, tmp_path):
        """Create temporary database for testing"""
        db_path = tmp_path / "test_trades.db"
        return str(db_path)
    
    def test_initialization(self, temp_db):
        """Test SQLiteLogger initialization"""
        logger = SQLiteLogger(temp_db)
        assert logger.db_path.exists()
        
        # Check if tables were created
        import sqlite3
        conn = sqlite3.connect(temp_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        assert 'trades' in tables
        assert 'orders' in tables
        assert 'market_states' in tables
        assert 'risk_events' in tables
        assert 'metrics' in tables
        
        conn.close()
    
    def test_log_trade_entry(self, temp_db):
        """Test logging trade entry"""
        logger = SQLiteLogger(temp_db)
        
        logger.log_trade_entry(
            trade_id='TEST001',
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=100,
            side='buy',
            entry_time=datetime.now(),
            entry_tag='grid_entry'
        )
        
        # Verify trade was logged
        stats = logger.get_trade_statistics()
        assert stats['total_trades'] == 1
    
    def test_log_trade_exit(self, temp_db):
        """Test logging trade exit"""
        logger = SQLiteLogger(temp_db)
        
        # First log entry
        logger.log_trade_entry(
            trade_id='TEST001',
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=100,
            side='buy',
            entry_time=datetime.now()
        )
        
        # Then log exit
        logger.log_trade_exit(
            trade_id='TEST001',
            pair='XRP/USDT',
            exit_price=0.51,
            exit_reason='take_profit',
            profit=1.0,
            timestamp=datetime.now()
        )
        
        # Verify statistics
        stats = logger.get_trade_statistics()
        assert stats['completed_trades'] == 1
        assert stats['winning_trades'] == 1
        assert stats['total_profit'] == 1.0


# MetricsTracker Tests
class TestMetricsTracker:
    """Test MetricsTracker component"""
    
    def test_initialization(self, sample_config):
        """Test MetricsTracker initialization"""
        tracker = MetricsTracker(sample_config['metrics'])
        assert tracker.total_trades == 0
        assert tracker.winning_trades == 0
        assert tracker.losing_trades == 0
        assert tracker.total_profit == 0.0
    
    def test_record_trade_exit(self, sample_config):
        """Test recording trade exit"""
        tracker = MetricsTracker(sample_config['metrics'])
        
        # Record winning trade
        tracker.record_trade_exit(profit=10.0, duration=3600)
        assert tracker.winning_trades == 1
        assert tracker.total_profit == 10.0
        
        # Record losing trade
        tracker.record_trade_exit(profit=-5.0, duration=1800)
        assert tracker.losing_trades == 1
        assert tracker.total_profit == 5.0
    
    def test_win_rate_calculation(self, sample_config):
        """Test win rate calculation"""
        tracker = MetricsTracker(sample_config['metrics'])
        
        # No trades yet
        assert tracker.calculate_win_rate() == 0.0
        
        # Add some trades
        tracker.winning_trades = 6
        tracker.losing_trades = 4
        assert tracker.calculate_win_rate() == 0.6
    
    def test_maker_ratio_calculation(self, sample_config):
        """Test maker ratio calculation"""
        tracker = MetricsTracker(sample_config['metrics'])
        
        # No orders yet
        assert tracker.calculate_maker_ratio() == 1.0  # Default assumption
        
        # Add some orders
        tracker.maker_orders = 95
        tracker.taker_orders = 5
        assert tracker.calculate_maker_ratio() == 0.95
    
    def test_metrics_summary(self, sample_config):
        """Test comprehensive metrics summary"""
        tracker = MetricsTracker(sample_config['metrics'])
        
        # Add some test data
        tracker.total_trades = 10
        tracker.winning_trades = 7
        tracker.losing_trades = 3
        tracker.total_profit = 100.0
        tracker.total_fees = 5.0
        
        summary = tracker.get_metrics_summary()
        
        assert summary['total_trades'] == 10
        assert summary['win_rate'] == 0.7
        assert summary['net_profit'] == 95.0


# Integration Tests
class TestIntegration:
    """Integration tests for components working together"""
    
    def test_spread_and_order_integration(self, sample_config):
        """Test SpreadManager and OrderManager integration"""
        spread_mgr = SpreadManager(sample_config['spread'])
        order_mgr = OrderManager(sample_config['order'], spread_mgr)
        
        # Build grid with spread validation
        grids = order_mgr.build_grid(
            pair='XRP/USDT',
            current_price=0.5,
            atr=0.01,
            account_balance=10000,
            best_bid=0.499,
            best_ask=0.501,
            existing_position=0
        )
        
        # Verify all grid prices maintain maker spread
        for grid in grids:
            if grid.side == OrderSide.BUY:
                assert grid.price < 0.499  # Below best bid
            else:
                assert grid.price > 0.501  # Above best ask
    
    def test_market_and_risk_integration(self, sample_config):
        """Test MarketDetector and RiskManager integration"""
        market = MarketDetector(sample_config['market'])
        risk = RiskManager(sample_config['risk'])
        
        # Create market conditions
        suitable_row = pd.Series({
            'is_sideways': True,
            'volatility': 0.02,
            'volume_ratio': 1.0,
            'adx': 20.0,
            'atr': 0.01
        })
        
        # Check if market is suitable
        if market.is_suitable_for_trading(suitable_row):
            # Evaluate risk for entry
            can_enter, _ = risk.evaluate_entry_risk(
                pair='XRP/USDT',
                entry_price=0.5,
                quantity=100,
                account_balance=10000,
                atr=suitable_row['atr'],
                existing_positions={}
            )
            assert can_enter == True


# Edge Case Tests
class TestEdgeCases:
    """Test edge cases and error handling"""
    
    def test_zero_balance_handling(self, sample_config):
        """Test handling of zero account balance"""
        order_mgr = OrderManager(sample_config['order'])
        
        grids = order_mgr.build_grid(
            pair='XRP/USDT',
            current_price=0.5,
            atr=0.01,
            account_balance=0,  # Zero balance
            best_bid=0.499,
            best_ask=0.501,
            existing_position=0
        )
        
        # Should return empty or minimal grids
        assert len(grids) == 0 or all(g.quantity == 0 for g in grids)
    
    def test_extreme_volatility_handling(self, sample_config):
        """Test handling of extreme volatility"""
        market = MarketDetector(sample_config['market'])
        
        extreme_row = pd.Series({
            'is_sideways': False,
            'volatility': 0.20,  # 20% volatility
            'volume_ratio': 1.0,
            'adx': 50.0,
            'atr': 0.05
        })
        
        # Should not be suitable for trading
        assert market.is_suitable_for_trading(extreme_row) == False
        assert market.should_exit_positions(extreme_row) == True
    
    def test_invalid_grid_level(self):
        """Test invalid grid level creation"""
        with pytest.raises(ValueError):
            GridLevel(
                level=0,
                price=-0.5,  # Invalid negative price
                quantity=100,
                side=OrderSide.BUY,
                distance_from_mid=0.01
            )
    
    def test_database_error_handling(self, temp_db):
        """Test database error handling"""
        logger = SQLiteLogger(temp_db)
        
        # Try to log with invalid data
        logger.log_trade_entry(
            trade_id=None,  # Invalid NULL trade_id
            pair='XRP/USDT',
            entry_price=0.5,
            quantity=100,
            side='buy',
            entry_time=datetime.now()
        )
        
        # Should handle gracefully without crashing
        stats = logger.get_trade_statistics()
        assert isinstance(stats, dict)  # Should return empty dict or valid stats


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
