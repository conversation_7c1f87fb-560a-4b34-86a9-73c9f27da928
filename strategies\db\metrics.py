"""
Metrics Tracker
Tracks and calculates performance metrics for the strategy
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
import numpy as np
from collections import deque
from prometheus_client import Counter, Gauge, Histogram, push_to_gateway, CollectorRegistry

logger = logging.getLogger(__name__)


@dataclass
class MetricsConfig:
    """Metrics configuration"""
    enable_prometheus: bool = False
    prometheus_gateway: str = "localhost:9091"
    metrics_window: int = 1000  # Number of trades to keep in memory
    update_interval: int = 60  # Seconds between metric updates
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'MetricsConfig':
        """Create config from dictionary"""
        return cls(**{k: v for k, v in config.items() if k in cls.__annotations__})


class MetricsTracker:
    """
    Tracks strategy performance metrics
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize metrics tracker
        
        Args:
            config: Metrics configuration dictionary
        """
        self.config = MetricsConfig.from_dict(config)
        
        # Trade metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit = 0.0
        self.total_fees = 0.0
        self.maker_orders = 0
        self.taker_orders = 0
        
        # Rolling windows
        self.recent_profits = deque(maxlen=self.config.metrics_window)
        self.recent_durations = deque(maxlen=self.config.metrics_window)
        self.recent_fills = deque(maxlen=100)
        
        # Session metrics
        self.session_start = datetime.now()
        self.session_high_balance = 0.0
        self.session_low_balance = 0.0
        self.max_drawdown = 0.0
        
        # Initialize Prometheus metrics if enabled
        if self.config.enable_prometheus:
            self._init_prometheus_metrics()
        
        logger.info("Metrics tracker initialized")
    
    def _init_prometheus_metrics(self) -> None:
        """Initialize Prometheus metrics"""
        self.registry = CollectorRegistry()
        
        # Counters
        self.trades_total = Counter(
            'freqtrade_trades_total',
            'Total number of trades',
            ['pair', 'side'],
            registry=self.registry
        )
        
        self.trades_won = Counter(
            'freqtrade_trades_won',
            'Number of winning trades',
            ['pair'],
            registry=self.registry
        )
        
        self.trades_lost = Counter(
            'freqtrade_trades_lost',
            'Number of losing trades',
            ['pair'],
            registry=self.registry
        )
        
        # Gauges
        self.current_profit = Gauge(
            'freqtrade_current_profit',
            'Current total profit',
            registry=self.registry
        )
        
        self.win_rate = Gauge(
            'freqtrade_win_rate',
            'Current win rate',
            registry=self.registry
        )
        
        self.maker_ratio = Gauge(
            'freqtrade_maker_ratio',
            'Ratio of maker orders',
            registry=self.registry
        )
        
        self.sharpe_ratio = Gauge(
            'freqtrade_sharpe_ratio',
            'Current Sharpe ratio',
            registry=self.registry
        )
        
        # Histograms
        self.trade_duration = Histogram(
            'freqtrade_trade_duration_seconds',
            'Trade duration in seconds',
            buckets=(60, 300, 900, 3600, 7200, 14400, 28800, 86400),
            registry=self.registry
        )
        
        self.trade_profit = Histogram(
            'freqtrade_trade_profit',
            'Trade profit distribution',
            buckets=(-0.05, -0.02, -0.01, -0.005, 0, 0.005, 0.01, 0.02, 0.05),
            registry=self.registry
        )
    
    def record_trade_entry(self, pair: str, side: str, amount: float, price: float) -> None:
        """Record trade entry"""
        self.total_trades += 1
        
        if self.config.enable_prometheus:
            self.trades_total.labels(pair=pair, side=side).inc()
    
    def record_trade_exit(self, profit: float, duration: int) -> None:
        """
        Record trade exit
        
        Args:
            profit: Trade profit
            duration: Trade duration in seconds
        """
        self.recent_profits.append(profit)
        self.recent_durations.append(duration)
        
        if profit > 0:
            self.winning_trades += 1
            if self.config.enable_prometheus:
                self.trades_won.labels(pair='all').inc()
        else:
            self.losing_trades += 1
            if self.config.enable_prometheus:
                self.trades_lost.labels(pair='all').inc()
        
        self.total_profit += profit
        
        if self.config.enable_prometheus:
            self.trade_profit.observe(profit)
            self.trade_duration.observe(duration)
    
    def record_order_fill(self, is_maker: bool, fee: float) -> None:
        """Record order fill"""
        if is_maker:
            self.maker_orders += 1
        else:
            self.taker_orders += 1
        
        self.total_fees += fee
        self.recent_fills.append(is_maker)
    
    def update_loop_metrics(self) -> None:
        """Update metrics in bot loop"""
        # Calculate current metrics
        win_rate = self.calculate_win_rate()
        sharpe = self.calculate_sharpe_ratio()
        maker_ratio = self.calculate_maker_ratio()
        
        # Update Prometheus metrics if enabled
        if self.config.enable_prometheus:
            self.current_profit.set(self.total_profit)
            self.win_rate.set(win_rate)
            self.maker_ratio.set(maker_ratio)
            if sharpe is not None:
                self.sharpe_ratio.set(sharpe)
            
            # Push to gateway
            try:
                push_to_gateway(
                    self.config.prometheus_gateway,
                    job='freqtrade_grid_maker',
                    registry=self.registry
                )
            except Exception as e:
                logger.error(f"Failed to push metrics to Prometheus: {e}")
    
    def calculate_win_rate(self) -> float:
        """Calculate win rate"""
        total_completed = self.winning_trades + self.losing_trades
        if total_completed == 0:
            return 0.0
        return self.winning_trades / total_completed
    
    def calculate_sharpe_ratio(self) -> Optional[float]:
        """Calculate Sharpe ratio"""
        if len(self.recent_profits) < 30:
            return None
        
        returns = np.array(list(self.recent_profits))
        if returns.std() == 0:
            return None
        
        # Assuming daily returns
        risk_free_rate = 0.02 / 252  # 2% annual risk-free rate
        sharpe = (returns.mean() - risk_free_rate) / returns.std() * np.sqrt(252)
        return sharpe
    
    def calculate_maker_ratio(self) -> float:
        """Calculate maker order ratio"""
        total_orders = self.maker_orders + self.taker_orders
        if total_orders == 0:
            return 1.0  # Assume all maker if no orders yet
        return self.maker_orders / total_orders
    
    def calculate_profit_factor(self) -> float:
        """Calculate profit factor"""
        gross_profit = sum(p for p in self.recent_profits if p > 0)
        gross_loss = abs(sum(p for p in self.recent_profits if p < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0
        return gross_profit / gross_loss
    
    def update_drawdown(self, current_balance: float) -> None:
        """Update drawdown metrics"""
        if current_balance > self.session_high_balance:
            self.session_high_balance = current_balance
        
        if self.session_high_balance > 0:
            current_drawdown = (self.session_high_balance - current_balance) / self.session_high_balance
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary"""
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.calculate_win_rate(),
            'total_profit': self.total_profit,
            'total_fees': self.total_fees,
            'net_profit': self.total_profit - self.total_fees,
            'maker_ratio': self.calculate_maker_ratio(),
            'sharpe_ratio': self.calculate_sharpe_ratio(),
            'profit_factor': self.calculate_profit_factor(),
            'max_drawdown': self.max_drawdown,
            'avg_trade_duration': np.mean(list(self.recent_durations)) if self.recent_durations else 0,
            'session_duration': (datetime.now() - self.session_start).total_seconds() / 3600  # Hours
        }
    
    def reset_session_metrics(self) -> None:
        """Reset session metrics"""
        self.session_start = datetime.now()
        self.session_high_balance = 0.0
        self.session_low_balance = 0.0
        self.max_drawdown = 0.0
        logger.info("Session metrics reset")
