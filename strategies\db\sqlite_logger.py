"""
SQLite Logger
Handles database persistence for trades, metrics, and analysis
"""

import sqlite3
from typing import Dict, List, Optional, Any
from datetime import datetime
from contextlib import contextmanager
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class SQLiteLogger:
    """
    SQLite database logger for trade and metrics persistence
    """
    
    def __init__(self, db_path: str = 'trades.db'):
        """
        Initialize SQLite logger
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
        
    def _init_database(self) -> None:
        """Initialize database schema"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Trades table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE,
                    pair TEXT NOT NULL,
                    entry_price REAL NOT NULL,
                    exit_price REAL,
                    quantity REAL NOT NULL,
                    side TEXT NOT NULL,
                    entry_time TIMESTAMP NOT NULL,
                    exit_time TIMESTAMP,
                    profit REAL,
                    profit_pct REAL,
                    fees REAL DEFAULT 0,
                    is_maker BOOLEAN DEFAULT 1,
                    entry_tag TEXT,
                    exit_reason TEXT,
                    indicators JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Orders table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id TEXT UNIQUE,
                    trade_id TEXT,
                    pair TEXT NOT NULL,
                    order_type TEXT NOT NULL,
                    side TEXT NOT NULL,
                    price REAL NOT NULL,
                    quantity REAL NOT NULL,
                    filled_quantity REAL DEFAULT 0,
                    status TEXT NOT NULL,
                    is_maker BOOLEAN,
                    fee REAL DEFAULT 0,
                    placed_at TIMESTAMP NOT NULL,
                    filled_at TIMESTAMP,
                    cancelled_at TIMESTAMP,
                    FOREIGN KEY (trade_id) REFERENCES trades(trade_id)
                )
            """)
            
            # Grid levels table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS grid_levels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pair TEXT NOT NULL,
                    level INTEGER NOT NULL,
                    price REAL NOT NULL,
                    quantity REAL NOT NULL,
                    side TEXT NOT NULL,
                    distance_from_mid REAL NOT NULL,
                    is_active BOOLEAN DEFAULT 0,
                    order_id TEXT,
                    placed_at TIMESTAMP,
                    filled_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Market states table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS market_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pair TEXT NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    state TEXT NOT NULL,
                    adx REAL,
                    atr REAL,
                    volatility REAL,
                    volume_ratio REAL,
                    rsi REAL,
                    bb_position REAL,
                    confidence_sideways REAL,
                    confidence_trending REAL,
                    is_suitable_trading BOOLEAN
                )
            """)
            
            # Risk events table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS risk_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP NOT NULL,
                    event_type TEXT NOT NULL,
                    position_id TEXT,
                    pair TEXT,
                    amount REAL,
                    reason TEXT,
                    risk_level TEXT,
                    details JSON
                )
            """)
            
            # Metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP NOT NULL,
                    metric_type TEXT NOT NULL,
                    pair TEXT,
                    value REAL NOT NULL,
                    details JSON
                )
            """)
            
            # Performance summary table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_summary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL UNIQUE,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    losing_trades INTEGER DEFAULT 0,
                    total_profit REAL DEFAULT 0,
                    total_fees REAL DEFAULT 0,
                    maker_ratio REAL DEFAULT 0,
                    avg_trade_duration INTEGER,
                    max_drawdown REAL,
                    sharpe_ratio REAL,
                    details JSON
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_pair ON trades(pair)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_entry_time ON trades(entry_time)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_trade_id ON orders(trade_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_market_states_pair_time ON market_states(pair, timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_metrics_type_time ON metrics(metric_type, timestamp)")
            
            conn.commit()
            logger.info(f"Database initialized at {self.db_path}")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection context manager"""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def log_trade_attempt(
        self,
        pair: str,
        side: str,
        price: float,
        amount: float,
        tag: Optional[str],
        timestamp: datetime
    ) -> None:
        """Log trade attempt"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO metrics (timestamp, metric_type, pair, value, details)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    timestamp,
                    'trade_attempt',
                    pair,
                    price * amount,
                    json.dumps({
                        'side': side,
                        'price': price,
                        'amount': amount,
                        'tag': tag
                    })
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to log trade attempt: {e}")
    
    def log_trade_entry(
        self,
        trade_id: str,
        pair: str,
        entry_price: float,
        quantity: float,
        side: str,
        entry_time: datetime,
        entry_tag: Optional[str] = None,
        indicators: Optional[Dict] = None
    ) -> None:
        """Log trade entry"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO trades (
                        trade_id, pair, entry_price, quantity, side,
                        entry_time, entry_tag, indicators, is_maker
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade_id, pair, entry_price, quantity, side,
                    entry_time, entry_tag,
                    json.dumps(indicators) if indicators else None,
                    True  # Assuming maker orders
                ))
                conn.commit()
                logger.info(f"Trade entry logged: {trade_id}")
        except Exception as e:
            logger.error(f"Failed to log trade entry: {e}")
    
    def log_trade_exit(
        self,
        trade_id: str,
        pair: str,
        exit_price: float,
        exit_reason: str,
        profit: float,
        timestamp: datetime
    ) -> None:
        """Log trade exit"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # Update trade record
                cursor.execute("""
                    UPDATE trades
                    SET exit_price = ?, exit_time = ?, profit = ?,
                        profit_pct = ?, exit_reason = ?
                    WHERE trade_id = ?
                """, (
                    exit_price, timestamp, profit,
                    profit * 100,  # Convert to percentage
                    exit_reason, trade_id
                ))
                
                # Log exit metric
                cursor.execute("""
                    INSERT INTO metrics (timestamp, metric_type, pair, value, details)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    timestamp, 'trade_exit', pair, profit,
                    json.dumps({'reason': exit_reason, 'price': exit_price})
                ))
                
                conn.commit()
                logger.info(f"Trade exit logged: {trade_id}")
        except Exception as e:
            logger.error(f"Failed to log trade exit: {e}")
    
    def log_order(
        self,
        order_id: str,
        trade_id: Optional[str],
        pair: str,
        order_type: str,
        side: str,
        price: float,
        quantity: float,
        timestamp: datetime
    ) -> None:
        """Log order placement"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO orders (
                        order_id, trade_id, pair, order_type, side,
                        price, quantity, status, placed_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order_id, trade_id, pair, order_type, side,
                    price, quantity, 'pending', timestamp
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to log order: {e}")
    
    def update_order_status(
        self,
        order_id: str,
        status: str,
        filled_quantity: float = 0,
        fee: float = 0,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Update order status"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                if status == 'filled':
                    cursor.execute("""
                        UPDATE orders
                        SET status = ?, filled_quantity = ?, fee = ?, filled_at = ?
                        WHERE order_id = ?
                    """, (status, filled_quantity, fee, timestamp, order_id))
                elif status == 'cancelled':
                    cursor.execute("""
                        UPDATE orders
                        SET status = ?, cancelled_at = ?
                        WHERE order_id = ?
                    """, (status, timestamp, order_id))
                else:
                    cursor.execute("""
                        UPDATE orders SET status = ? WHERE order_id = ?
                    """, (status, order_id))
                
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to update order status: {e}")
    
    def log_market_state(
        self,
        pair: str,
        state: str,
        indicators: Dict[str, float],
        timestamp: datetime
    ) -> None:
        """Log market state"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO market_states (
                        pair, timestamp, state, adx, atr, volatility,
                        volume_ratio, rsi, bb_position, confidence_sideways,
                        confidence_trending, is_suitable_trading
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    pair, timestamp, state,
                    indicators.get('adx'), indicators.get('atr'),
                    indicators.get('volatility'), indicators.get('volume_ratio'),
                    indicators.get('rsi'), indicators.get('bb_position'),
                    indicators.get('confidence_sideways'),
                    indicators.get('confidence_trending'),
                    indicators.get('is_suitable_trading')
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to log market state: {e}")
    
    def log_risk_event(
        self,
        event_type: str,
        position_id: Optional[str],
        pair: Optional[str],
        amount: Optional[float],
        reason: str,
        risk_level: str,
        details: Optional[Dict],
        timestamp: datetime
    ) -> None:
        """Log risk event"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO risk_events (
                        timestamp, event_type, position_id, pair,
                        amount, reason, risk_level, details
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    timestamp, event_type, position_id, pair,
                    amount, reason, risk_level,
                    json.dumps(details) if details else None
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to log risk event: {e}")
    
    def get_trade_statistics(
        self,
        pair: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get trade statistics"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM trades WHERE 1=1"
                params = []
                
                if pair:
                    query += " AND pair = ?"
                    params.append(pair)
                if start_date:
                    query += " AND entry_time >= ?"
                    params.append(start_date)
                if end_date:
                    query += " AND entry_time <= ?"
                    params.append(end_date)
                
                cursor.execute(query, params)
                trades = cursor.fetchall()
                
                if not trades:
                    return {}
                
                # Calculate statistics
                total_trades = len(trades)
                completed_trades = [t for t in trades if t['exit_price'] is not None]
                winning_trades = [t for t in completed_trades if t['profit'] > 0]
                losing_trades = [t for t in completed_trades if t['profit'] < 0]
                
                total_profit = sum(t['profit'] for t in completed_trades if t['profit'])
                total_fees = sum(t['fees'] for t in trades if t['fees'])
                
                return {
                    'total_trades': total_trades,
                    'completed_trades': len(completed_trades),
                    'winning_trades': len(winning_trades),
                    'losing_trades': len(losing_trades),
                    'win_rate': len(winning_trades) / len(completed_trades) if completed_trades else 0,
                    'total_profit': total_profit,
                    'total_fees': total_fees,
                    'net_profit': total_profit - total_fees,
                    'avg_profit': total_profit / len(completed_trades) if completed_trades else 0,
                    'maker_ratio': sum(1 for t in trades if t['is_maker']) / total_trades
                }
        except Exception as e:
            logger.error(f"Failed to get trade statistics: {e}")
            return {}
