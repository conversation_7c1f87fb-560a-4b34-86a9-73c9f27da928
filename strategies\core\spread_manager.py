"""
Spread Manager Component
Handles spread calculations, validation, and dynamic adjustments
"""

from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass
import logging
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class SpreadConfig:
    """Spread manager configuration"""
    min_spread_bps: float = 10.0  # Minimum spread in basis points (0.1%)
    max_spread_bps: float = 50.0  # Maximum spread in basis points (0.5%)
    atr_multiplier: float = 0.5  # ATR multiplier for dynamic spread
    volatility_adjustment: bool = True  # Adjust spread based on volatility
    liquidity_adjustment: bool = True  # Adjust spread based on liquidity
    maker_fee: float = 0.0  # Maker fee (0 for zero-fee pairs)
    taker_fee: float = 0.001  # Taker fee (0.1% default)
    safety_margin_bps: float = 2.0  # Safety margin to ensure maker execution
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'SpreadConfig':
        """Create config from dictionary"""
        return cls(**{k: v for k, v in config.items() if k in cls.__annotations__})


class SpreadManager:
    """
    Manages spread calculations to ensure maker-only execution
    while maximizing fill probability
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize spread manager
        
        Args:
            config: Spread configuration dictionary
        """
        self.config = SpreadConfig.from_dict(config)
        self._spread_history = []
        self._last_optimal_spread = None
        
    def calculate_optimal_spread(
        self,
        current_price: float,
        best_bid: float,
        best_ask: float,
        atr: float,
        volume_24h: float,
        orderbook_depth: Optional[Dict] = None
    ) -> Tuple[float, float]:
        """
        Calculate optimal bid and ask spreads
        
        Args:
            current_price: Current market price
            best_bid: Current best bid
            best_ask: Current best ask
            atr: Average True Range
            volume_24h: 24-hour trading volume
            orderbook_depth: Optional orderbook depth data
            
        Returns:
            Tuple of (bid_spread, ask_spread) in price units
        """
        # Calculate base spread from market
        market_spread = best_ask - best_bid
        market_spread_bps = (market_spread / current_price) * 10000
        
        # Calculate minimum viable spread (must exceed taker fee + safety margin)
        min_viable_spread_bps = (
            self.config.taker_fee * 10000 + 
            self.config.safety_margin_bps
        )
        
        # Calculate dynamic spread based on ATR
        atr_spread = atr * self.config.atr_multiplier
        atr_spread_bps = (atr_spread / current_price) * 10000
        
        # Start with the larger of market spread and ATR-based spread
        base_spread_bps = max(market_spread_bps, atr_spread_bps)
        
        # Apply volatility adjustment
        if self.config.volatility_adjustment:
            volatility_factor = self._calculate_volatility_factor(atr, current_price)
            base_spread_bps *= volatility_factor
        
        # Apply liquidity adjustment
        if self.config.liquidity_adjustment:
            liquidity_factor = self._calculate_liquidity_factor(
                volume_24h, orderbook_depth
            )
            base_spread_bps *= liquidity_factor
        
        # Ensure spread is within configured bounds
        optimal_spread_bps = np.clip(
            base_spread_bps,
            max(self.config.min_spread_bps, min_viable_spread_bps),
            self.config.max_spread_bps
        )
        
        # Convert to price units
        spread_amount = (optimal_spread_bps / 10000) * current_price
        
        # Calculate separate bid and ask spreads
        # Can be asymmetric based on market conditions
        bid_spread, ask_spread = self._calculate_asymmetric_spreads(
            spread_amount, market_spread, best_bid, best_ask
        )
        
        # Store for analysis
        self._spread_history.append({
            'timestamp': np.datetime64('now'),
            'spread_bps': optimal_spread_bps,
            'bid_spread': bid_spread,
            'ask_spread': ask_spread
        })
        
        self._last_optimal_spread = (bid_spread, ask_spread)
        
        logger.debug(
            f"Calculated optimal spread: {optimal_spread_bps:.2f} bps "
            f"(bid: {bid_spread:.8f}, ask: {ask_spread:.8f})"
        )
        
        return bid_spread, ask_spread
    
    def _calculate_volatility_factor(self, atr: float, price: float) -> float:
        """
        Calculate volatility adjustment factor
        
        Args:
            atr: Average True Range
            price: Current price
            
        Returns:
            Volatility factor (1.0 = no adjustment)
        """
        # Calculate ATR as percentage of price
        atr_pct = (atr / price) * 100
        
        # Low volatility: tighten spread
        if atr_pct < 0.5:
            return 0.8
        # Normal volatility: no adjustment
        elif atr_pct < 2.0:
            return 1.0
        # High volatility: widen spread
        elif atr_pct < 5.0:
            return 1.2
        # Very high volatility: significantly widen spread
        else:
            return 1.5
    
    def _calculate_liquidity_factor(
        self,
        volume_24h: float,
        orderbook_depth: Optional[Dict]
    ) -> float:
        """
        Calculate liquidity adjustment factor
        
        Args:
            volume_24h: 24-hour trading volume
            orderbook_depth: Orderbook depth data
            
        Returns:
            Liquidity factor (1.0 = no adjustment)
        """
        # Base factor on volume
        if volume_24h < 100000:  # Low volume
            volume_factor = 1.3
        elif volume_24h < 1000000:  # Medium volume
            volume_factor = 1.0
        else:  # High volume
            volume_factor = 0.9
        
        # Adjust based on orderbook depth if available
        if orderbook_depth:
            bid_depth = orderbook_depth.get('bids_value', 0)
            ask_depth = orderbook_depth.get('asks_value', 0)
            total_depth = bid_depth + ask_depth
            
            if total_depth < 10000:  # Thin orderbook
                depth_factor = 1.2
            elif total_depth < 100000:  # Normal orderbook
                depth_factor = 1.0
            else:  # Deep orderbook
                depth_factor = 0.95
        else:
            depth_factor = 1.0
        
        return volume_factor * depth_factor
    
    def _calculate_asymmetric_spreads(
        self,
        total_spread: float,
        market_spread: float,
        best_bid: float,
        best_ask: float
    ) -> Tuple[float, float]:
        """
        Calculate potentially asymmetric bid and ask spreads
        
        Args:
            total_spread: Total spread amount
            market_spread: Current market spread
            best_bid: Best bid price
            best_ask: Best ask price
            
        Returns:
            Tuple of (bid_spread, ask_spread)
        """
        # Check for orderbook imbalance
        mid_price = (best_bid + best_ask) / 2
        bid_distance = mid_price - best_bid
        ask_distance = best_ask - mid_price
        
        if bid_distance > ask_distance * 1.2:
            # More room on bid side, can be more aggressive
            bid_ratio = 0.4
            ask_ratio = 0.6
        elif ask_distance > bid_distance * 1.2:
            # More room on ask side, can be more aggressive
            bid_ratio = 0.6
            ask_ratio = 0.4
        else:
            # Balanced orderbook
            bid_ratio = 0.5
            ask_ratio = 0.5
        
        bid_spread = total_spread * bid_ratio
        ask_spread = total_spread * ask_ratio
        
        return bid_spread, ask_spread
    
    def validate_spread(
        self,
        bid_price: float,
        ask_price: float,
        best_bid: float,
        best_ask: float
    ) -> bool:
        """
        Validate that proposed prices maintain maker status
        
        Args:
            bid_price: Proposed bid price
            ask_price: Proposed ask price
            best_bid: Current best bid
            best_ask: Current best ask
            
        Returns:
            True if spread is valid for maker execution
        """
        # Ensure bid is below best bid (maker)
        if bid_price >= best_bid:
            logger.warning(f"Bid price {bid_price} would cross spread (best bid: {best_bid})")
            return False
        
        # Ensure ask is above best ask (maker)
        if ask_price <= best_ask:
            logger.warning(f"Ask price {ask_price} would cross spread (best ask: {best_ask})")
            return False
        
        # Ensure minimum spread is maintained
        spread = ask_price - bid_price
        min_spread = bid_price * (self.config.min_spread_bps / 10000)
        
        if spread < min_spread:
            logger.warning(f"Spread {spread} below minimum {min_spread}")
            return False
        
        return True
    
    def adjust_price_for_maker(
        self,
        price: float,
        side: str,
        best_bid: float,
        best_ask: float,
        tick_size: float = 0.00000001
    ) -> float:
        """
        Adjust price to ensure maker execution
        
        Args:
            price: Proposed price
            side: 'buy' or 'sell'
            best_bid: Current best bid
            best_ask: Current best ask
            tick_size: Minimum price increment
            
        Returns:
            Adjusted price for maker execution
        """
        if side.lower() == 'buy':
            # For buy orders, ensure price is below best bid
            if price >= best_bid:
                adjusted_price = best_bid - tick_size
                logger.debug(f"Adjusted buy price from {price} to {adjusted_price}")
                return adjusted_price
        else:
            # For sell orders, ensure price is above best ask
            if price <= best_ask:
                adjusted_price = best_ask + tick_size
                logger.debug(f"Adjusted sell price from {price} to {adjusted_price}")
                return adjusted_price
        
        return price
    
    def get_spread_statistics(self) -> Dict[str, Any]:
        """
        Get spread statistics
        
        Returns:
            Dictionary of spread statistics
        """
        if not self._spread_history:
            return {}
        
        spreads_bps = [s['spread_bps'] for s in self._spread_history[-100:]]
        
        return {
            'current_spread': self._last_optimal_spread,
            'avg_spread_bps': np.mean(spreads_bps),
            'min_spread_bps': np.min(spreads_bps),
            'max_spread_bps': np.max(spreads_bps),
            'std_spread_bps': np.std(spreads_bps),
            'samples': len(spreads_bps)
        }
    
    def should_widen_spread(
        self,
        recent_fills: int,
        target_fill_rate: float = 0.8,
        evaluation_period: int = 100
    ) -> bool:
        """
        Determine if spread should be widened based on fill rate
        
        Args:
            recent_fills: Number of recent fills
            target_fill_rate: Target fill rate
            evaluation_period: Number of orders to evaluate
            
        Returns:
            True if spread should be widened
        """
        if evaluation_period == 0:
            return False
        
        fill_rate = recent_fills / evaluation_period
        
        # If we're getting filled too often, we might be too aggressive
        if fill_rate > target_fill_rate * 1.2:
            logger.info(f"High fill rate {fill_rate:.2%}, considering widening spread")
            return True
        
        return False
    
    def should_tighten_spread(
        self,
        recent_fills: int,
        target_fill_rate: float = 0.8,
        evaluation_period: int = 100
    ) -> bool:
        """
        Determine if spread should be tightened based on fill rate
        
        Args:
            recent_fills: Number of recent fills
            target_fill_rate: Target fill rate
            evaluation_period: Number of orders to evaluate
            
        Returns:
            True if spread should be tightened
        """
        if evaluation_period == 0:
            return False
        
        fill_rate = recent_fills / evaluation_period
        
        # If we're not getting filled enough, we might be too conservative
        if fill_rate < target_fill_rate * 0.5:
            logger.info(f"Low fill rate {fill_rate:.2%}, considering tightening spread")
            return True
        
        return False
