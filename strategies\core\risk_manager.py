"""
Risk Manager Component
Handles risk management, stop losses, position sizing, and exposure control
"""

from typing import Dict, Optional, Any, List, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import numpy as np

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskConfig:
    """Risk manager configuration"""
    max_position_pct: float = 0.15  # Max 15% of portfolio in one position
    max_total_exposure_pct: float = 0.5  # Max 50% total exposure
    stop_loss_atr_mult: float = 3.0  # Stop loss at 3x ATR
    take_profit_atr_mult: float = 5.0  # Take profit at 5x ATR (optional)
    trailing_stop_enable: bool = False  # Enable trailing stop
    trailing_stop_positive: float = 0.01  # Start trailing at 1% profit
    trailing_stop_offset: float = -0.005  # Trail by 0.5%
    max_drawdown_pct: float = 0.10  # Max 10% drawdown
    max_daily_loss_pct: float = 0.05  # Max 5% daily loss
    max_consecutive_losses: int = 5  # Max consecutive losses before pause
    risk_free_rate: float = 0.02  # Annual risk-free rate for Sharpe calculation
    min_risk_reward_ratio: float = 1.5  # Minimum risk/reward ratio
    emergency_exit_volatility_mult: float = 3.0  # Exit if volatility > 3x normal
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'RiskConfig':
        """Create config from dictionary"""
        return cls(**{k: v for k, v in config.items() if k in cls.__annotations__})


@dataclass
class PositionRisk:
    """Risk metrics for a position"""
    position_id: str
    pair: str
    entry_price: float
    current_price: float
    quantity: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    risk_amount: float
    risk_pct: float
    time_in_position: timedelta
    risk_level: RiskLevel


class RiskManager:
    """
    Manages trading risk, stop losses, and portfolio exposure
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize risk manager
        
        Args:
            config: Risk configuration dictionary
        """
        self.config = RiskConfig.from_dict(config)
        self.positions: Dict[str, PositionRisk] = {}
        self.daily_pnl: float = 0.0
        self.session_pnl: float = 0.0
        self.max_drawdown: float = 0.0
        self.consecutive_losses: int = 0
        self.last_reset_time: datetime = datetime.now()
        self.pnl_history: List[float] = []
        self.risk_events: List[Dict] = []
        
    def evaluate_entry_risk(
        self,
        pair: str,
        entry_price: float,
        quantity: float,
        account_balance: float,
        atr: float,
        existing_positions: Dict[str, float]
    ) -> Tuple[bool, str]:
        """
        Evaluate if a new position can be taken based on risk rules
        
        Args:
            pair: Trading pair
            entry_price: Entry price
            quantity: Position size
            account_balance: Account balance
            atr: Average True Range
            existing_positions: Existing positions
            
        Returns:
            Tuple of (can_enter, reason)
        """
        # Calculate position value
        position_value = entry_price * quantity
        position_pct = position_value / account_balance
        
        # Check single position size limit
        if position_pct > self.config.max_position_pct:
            reason = f"Position size {position_pct:.2%} exceeds max {self.config.max_position_pct:.2%}"
            logger.warning(reason)
            return False, reason
        
        # Calculate total exposure including new position
        total_exposure = sum(existing_positions.values()) + position_value
        total_exposure_pct = total_exposure / account_balance
        
        # Check total exposure limit
        if total_exposure_pct > self.config.max_total_exposure_pct:
            reason = f"Total exposure {total_exposure_pct:.2%} would exceed max {self.config.max_total_exposure_pct:.2%}"
            logger.warning(reason)
            return False, reason
        
        # Check daily loss limit
        if self.daily_pnl < -self.config.max_daily_loss_pct * account_balance:
            reason = f"Daily loss limit reached ({self.daily_pnl:.2f})"
            logger.warning(reason)
            return False, reason
        
        # Check consecutive losses
        if self.consecutive_losses >= self.config.max_consecutive_losses:
            reason = f"Max consecutive losses reached ({self.consecutive_losses})"
            logger.warning(reason)
            return False, reason
        
        # Check session drawdown
        if self.session_pnl < -self.config.max_drawdown_pct * account_balance:
            reason = f"Max drawdown reached ({self.session_pnl:.2f})"
            logger.warning(reason)
            return False, reason
        
        # Calculate risk/reward ratio
        stop_loss_distance = atr * self.config.stop_loss_atr_mult
        take_profit_distance = atr * self.config.take_profit_atr_mult
        risk_reward_ratio = take_profit_distance / stop_loss_distance
        
        if risk_reward_ratio < self.config.min_risk_reward_ratio:
            reason = f"Risk/reward ratio {risk_reward_ratio:.2f} below minimum {self.config.min_risk_reward_ratio}"
            logger.debug(reason)
            return False, reason
        
        return True, "Risk check passed"
    
    def calculate_stop_loss(
        self,
        entry_price: float,
        side: str,
        atr: float,
        support_level: Optional[float] = None
    ) -> float:
        """
        Calculate stop loss price
        
        Args:
            entry_price: Entry price
            side: 'buy' or 'sell'
            atr: Average True Range
            support_level: Optional support/resistance level
            
        Returns:
            Stop loss price
        """
        stop_distance = atr * self.config.stop_loss_atr_mult
        
        if side.lower() == 'buy':
            # For long positions, stop below entry
            stop_price = entry_price - stop_distance
            
            # Use support level if provided and more conservative
            if support_level and support_level > stop_price:
                stop_price = support_level - (atr * 0.1)  # Small buffer below support
        else:
            # For short positions, stop above entry
            stop_price = entry_price + stop_distance
            
            # Use resistance level if provided and more conservative
            if support_level and support_level < stop_price:
                stop_price = support_level + (atr * 0.1)  # Small buffer above resistance
        
        return stop_price
    
    def calculate_take_profit(
        self,
        entry_price: float,
        side: str,
        atr: float,
        resistance_level: Optional[float] = None
    ) -> Optional[float]:
        """
        Calculate take profit price
        
        Args:
            entry_price: Entry price
            side: 'buy' or 'sell'
            atr: Average True Range
            resistance_level: Optional resistance/support level
            
        Returns:
            Take profit price or None if not using
        """
        if self.config.take_profit_atr_mult <= 0:
            return None
        
        tp_distance = atr * self.config.take_profit_atr_mult
        
        if side.lower() == 'buy':
            # For long positions, TP above entry
            tp_price = entry_price + tp_distance
            
            # Use resistance level if provided and more conservative
            if resistance_level and resistance_level < tp_price:
                tp_price = resistance_level - (atr * 0.1)  # Small buffer below resistance
        else:
            # For short positions, TP below entry
            tp_price = entry_price - tp_distance
            
            # Use support level if provided and more conservative
            if resistance_level and resistance_level > tp_price:
                tp_price = resistance_level + (atr * 0.1)  # Small buffer above support
        
        return tp_price
    
    def evaluate_exit_conditions(
        self,
        position_id: str,
        current_price: float,
        atr: float,
        volatility: float
    ) -> Optional[str]:
        """
        Evaluate if position should be exited
        
        Args:
            position_id: Position identifier
            current_price: Current price
            atr: Current ATR
            volatility: Current volatility
            
        Returns:
            Exit reason or None
        """
        if position_id not in self.positions:
            return None
        
        position = self.positions[position_id]
        
        # Check stop loss
        if position.stop_loss_price:
            if (position.entry_price > current_price and 
                current_price <= position.stop_loss_price):
                self._record_loss(position)
                return "stop_loss_hit"
        
        # Check take profit
        if position.take_profit_price:
            if (position.entry_price < current_price and 
                current_price >= position.take_profit_price):
                self._record_profit(position)
                return "take_profit_hit"
        
        # Check emergency volatility exit
        normal_volatility = atr / position.entry_price
        if volatility > normal_volatility * self.config.emergency_exit_volatility_mult:
            logger.warning(f"Emergency exit due to high volatility: {volatility:.4f}")
            return "emergency_volatility_exit"
        
        # Check time-based exit (optional)
        if position.time_in_position > timedelta(hours=24):
            if position.unrealized_pnl_pct < -0.02:  # Down more than 2% after 24h
                return "time_stop_loss"
        
        # Check drawdown exit
        if position.unrealized_pnl_pct < -self.config.max_drawdown_pct:
            return "max_drawdown_exit"
        
        return None
    
    def update_position(
        self,
        position_id: str,
        pair: str,
        entry_price: float,
        current_price: float,
        quantity: float,
        entry_time: datetime
    ) -> PositionRisk:
        """
        Update or create position risk metrics
        
        Args:
            position_id: Position identifier
            pair: Trading pair
            entry_price: Entry price
            current_price: Current price
            quantity: Position quantity
            entry_time: Entry timestamp
            
        Returns:
            Updated PositionRisk object
        """
        # Calculate PnL
        if quantity > 0:  # Long position
            unrealized_pnl = (current_price - entry_price) * quantity
        else:  # Short position
            unrealized_pnl = (entry_price - current_price) * abs(quantity)
        
        unrealized_pnl_pct = unrealized_pnl / (entry_price * abs(quantity))
        
        # Calculate risk metrics
        position_value = current_price * abs(quantity)
        risk_amount = entry_price * abs(quantity) * self.config.max_position_pct
        risk_pct = abs(unrealized_pnl_pct)
        
        # Determine risk level
        if risk_pct < 0.02:
            risk_level = RiskLevel.LOW
        elif risk_pct < 0.05:
            risk_level = RiskLevel.MEDIUM
        elif risk_pct < 0.10:
            risk_level = RiskLevel.HIGH
        else:
            risk_level = RiskLevel.CRITICAL
        
        # Create/update position
        position = PositionRisk(
            position_id=position_id,
            pair=pair,
            entry_price=entry_price,
            current_price=current_price,
            quantity=quantity,
            unrealized_pnl=unrealized_pnl,
            unrealized_pnl_pct=unrealized_pnl_pct,
            stop_loss_price=None,  # Set separately
            take_profit_price=None,  # Set separately
            risk_amount=risk_amount,
            risk_pct=risk_pct,
            time_in_position=datetime.now() - entry_time,
            risk_level=risk_level
        )
        
        self.positions[position_id] = position
        return position
    
    def _record_loss(self, position: PositionRisk) -> None:
        """Record a loss for statistics"""
        self.consecutive_losses += 1
        self.daily_pnl += position.unrealized_pnl
        self.session_pnl += position.unrealized_pnl
        self.pnl_history.append(position.unrealized_pnl)
        
        self.risk_events.append({
            'timestamp': datetime.now(),
            'type': 'loss',
            'position_id': position.position_id,
            'amount': position.unrealized_pnl,
            'reason': 'stop_loss'
        })
        
        # Update max drawdown
        if self.session_pnl < self.max_drawdown:
            self.max_drawdown = self.session_pnl
    
    def _record_profit(self, position: PositionRisk) -> None:
        """Record a profit for statistics"""
        self.consecutive_losses = 0  # Reset consecutive losses
        self.daily_pnl += position.unrealized_pnl
        self.session_pnl += position.unrealized_pnl
        self.pnl_history.append(position.unrealized_pnl)
        
        self.risk_events.append({
            'timestamp': datetime.now(),
            'type': 'profit',
            'position_id': position.position_id,
            'amount': position.unrealized_pnl,
            'reason': 'take_profit'
        })
    
    def get_portfolio_risk_metrics(self) -> Dict[str, Any]:
        """
        Get current portfolio risk metrics
        
        Returns:
            Dictionary of risk metrics
        """
        total_exposure = sum(p.current_price * abs(p.quantity) for p in self.positions.values())
        total_risk = sum(p.risk_amount for p in self.positions.values())
        total_unrealized_pnl = sum(p.unrealized_pnl for p in self.positions.values())
        
        # Calculate Sharpe ratio if enough history
        sharpe_ratio = self._calculate_sharpe_ratio()
        
        # Calculate win rate
        wins = sum(1 for pnl in self.pnl_history if pnl > 0)
        losses = sum(1 for pnl in self.pnl_history if pnl < 0)
        win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0
        
        return {
            'total_positions': len(self.positions),
            'total_exposure': total_exposure,
            'total_risk': total_risk,
            'total_unrealized_pnl': total_unrealized_pnl,
            'daily_pnl': self.daily_pnl,
            'session_pnl': self.session_pnl,
            'max_drawdown': self.max_drawdown,
            'consecutive_losses': self.consecutive_losses,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'total_trades': len(self.pnl_history),
            'risk_level': self._get_portfolio_risk_level()
        }
    
    def _calculate_sharpe_ratio(self) -> Optional[float]:
        """Calculate Sharpe ratio from PnL history"""
        if len(self.pnl_history) < 30:
            return None
        
        returns = np.array(self.pnl_history)
        if returns.std() == 0:
            return None
        
        # Annualized Sharpe ratio (assuming daily returns)
        sharpe = (returns.mean() - self.config.risk_free_rate/252) / returns.std() * np.sqrt(252)
        return sharpe
    
    def _get_portfolio_risk_level(self) -> RiskLevel:
        """Determine overall portfolio risk level"""
        if not self.positions:
            return RiskLevel.LOW
        
        # Check various risk factors
        risk_score = 0
        
        # Consecutive losses
        if self.consecutive_losses >= self.config.max_consecutive_losses * 0.8:
            risk_score += 3
        elif self.consecutive_losses >= self.config.max_consecutive_losses * 0.5:
            risk_score += 2
        
        # Drawdown
        if self.max_drawdown < -self.config.max_drawdown_pct * 0.8:
            risk_score += 3
        elif self.max_drawdown < -self.config.max_drawdown_pct * 0.5:
            risk_score += 2
        
        # Position risk levels
        critical_positions = sum(1 for p in self.positions.values() if p.risk_level == RiskLevel.CRITICAL)
        high_risk_positions = sum(1 for p in self.positions.values() if p.risk_level == RiskLevel.HIGH)
        
        if critical_positions > 0:
            risk_score += 3
        if high_risk_positions > len(self.positions) * 0.3:
            risk_score += 2
        
        # Map score to risk level
        if risk_score >= 6:
            return RiskLevel.CRITICAL
        elif risk_score >= 4:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def reset_daily_metrics(self) -> None:
        """Reset daily metrics (call at start of trading day)"""
        self.daily_pnl = 0.0
        self.last_reset_time = datetime.now()
        logger.info("Daily risk metrics reset")
    
    def should_pause_trading(self) -> bool:
        """
        Determine if trading should be paused due to risk
        
        Returns:
            True if trading should be paused
        """
        risk_level = self._get_portfolio_risk_level()
        
        if risk_level == RiskLevel.CRITICAL:
            logger.error("Critical risk level - pausing trading")
            return True
        
        if self.consecutive_losses >= self.config.max_consecutive_losses:
            logger.error(f"Max consecutive losses reached ({self.consecutive_losses}) - pausing trading")
            return True
        
        return False
