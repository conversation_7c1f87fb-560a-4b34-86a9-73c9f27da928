services:
  freqtrade:
    build: .
    container_name: freqtrade-grid-maker
    restart: unless-stopped
    volumes:
      - ./user_data:/app/user_data
      - ./strategies:/app/strategies
      - ./config.json:/app/config.json:ro
      - ./trades.db:/app/trades.db
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=UTC
    networks:
      - freqtrade-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  prometheus:
    image: prom/prometheus:latest
    container_name: freqtrade-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    networks:
      - freqtrade-network

  grafana:
    image: grafana/grafana:latest
    container_name: freqtrade-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=
    networks:
      - freqtrade-network

networks:
  freqtrade-network:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data:
