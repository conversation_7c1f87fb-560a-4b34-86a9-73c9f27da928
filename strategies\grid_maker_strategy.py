"""
Grid Maker Strategy
Main strategy implementation combining all components for zero-fee grid trading
"""

from typing import Dict, Optional, Any, Union, Tuple
from datetime import datetime
import logging
import pandas as pd
from pandas import DataFrame, Series

# Import base and components
from strategies.core.base_strategy import BaseStrategy
from strategies.core.order_manager import OrderManager
from strategies.core.spread_manager import Spread<PERSON>anager
from strategies.core.market_detector import MarketDetector
from strategies.core.risk_manager import RiskManager
from strategies.db.sqlite_logger import SQLiteLogger
from strategies.db.metrics import MetricsTracker

from freqtrade.persistence import Trade
from freqtrade.exchange import timeframe_to_minutes

logger = logging.getLogger(__name__)


class GridMakerStrategy(BaseStrategy):
    """
    Zero-fee grid trading strategy optimized for sideways markets
    
    Features:
    - Dynamic grid placement based on market conditions
    - Maker-only order execution to avoid fees
    - Advanced risk management with ATR-based stops
    - Market state detection (sideways/trending)
    - Comprehensive metrics tracking
    """
    
    # Strategy identification
    strategy_name = "GridMakerStrategy"
    strategy_version = "1.0.0"
    
    # Freqtrade configuration
    minimal_roi = {"0": 100}  # Disable ROI
    stoploss = -0.99  # Use custom stop loss
    trailing_stop = False
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = True
    
    # Timeframe
    timeframe = '1m'
    
    # Position adjustment
    position_adjustment_enable = True
    max_entry_position_adjustment = 3
    
    # Order types
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'limit',
        'stoploss_on_exchange': True,
        'stoploss_on_exchange_interval': 60
    }
    
    # Optional order time in force
    order_time_in_force = {
        'entry': 'GTC',  # Good till cancelled
        'exit': 'GTC'
    }
    
    def _initialize_components(self) -> None:
        """Initialize all strategy components"""
        try:
            # Initialize components with configuration
            self.market_detector = MarketDetector(self.strategy_config.market_config)
            self.spread_manager = SpreadManager(self.strategy_config.spread_config)
            self.order_manager = OrderManager(
                self.strategy_config.order_config,
                self.spread_manager
            )
            self.risk_manager = RiskManager(self.strategy_config.risk_config)
            
            # Initialize database and metrics
            self.db_logger = SQLiteLogger('trades.db')
            self.metrics = MetricsTracker(self.strategy_config.metrics_config)
            
            # State tracking
            self.last_order_time = {}
            self.grid_active = {}
            self.position_tracker = {}
            
            logger.info(f"{self.strategy_name} v{self.strategy_version} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Add all technical indicators
        
        Args:
            dataframe: OHLCV dataframe
            metadata: Pair metadata
            
        Returns:
            DataFrame with indicators
        """
        pair = metadata.get('pair', 'UNKNOWN')
        
        # Add market detection indicators
        dataframe = self.market_detector.compute_indicators(dataframe)
        
        # Add custom indicators for grid placement
        dataframe['mid_price'] = (dataframe['high'] + dataframe['low']) / 2
        dataframe['spread'] = dataframe['high'] - dataframe['low']
        dataframe['spread_pct'] = dataframe['spread'] / dataframe['close'] * 100
        
        # Volume analysis
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # Price levels for support/resistance
        dataframe['resistance'] = dataframe['high'].rolling(window=20).max()
        dataframe['support'] = dataframe['low'].rolling(window=20).min()
        
        # Trading intensity
        dataframe['trading_intensity'] = dataframe.apply(
            lambda row: self.market_detector.get_trading_intensity(row),
            axis=1
        )
        
        logger.debug(f"Indicators populated for {pair}")
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Define entry signals for grid trading
        
        Args:
            dataframe: DataFrame with indicators
            metadata: Pair metadata
            
        Returns:
            DataFrame with entry signals
        """
        pair = metadata.get('pair', 'UNKNOWN')
        
        # Entry conditions
        conditions = []
        
        # Market must be sideways
        conditions.append(dataframe['is_sideways'] == True)
        
        # ADX below threshold (no strong trend)
        conditions.append(dataframe['adx'] < 25)
        
        # Volume must be sufficient
        conditions.append(dataframe['volume_ratio'] > 0.5)
        
        # Volatility within acceptable range
        conditions.append(dataframe['volatility'] < 0.03)
        
        # Trading intensity must be positive
        conditions.append(dataframe['trading_intensity'] > 0)
        
        # RSI in neutral zone (not overbought/oversold)
        conditions.append((dataframe['rsi'] > 30) & (dataframe['rsi'] < 70))
        
        # Price within Bollinger Bands
        conditions.append((dataframe['bb_position'] > 0.1) & (dataframe['bb_position'] < 0.9))
        
        # Combine all conditions
        if conditions:
            dataframe.loc[
                reduce(lambda x, y: x & y, conditions),
                ['enter_long', 'enter_tag']
            ] = (1, 'grid_entry')
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Define exit signals
        
        Args:
            dataframe: DataFrame with indicators
            metadata: Pair metadata
            
        Returns:
            DataFrame with exit signals
        """
        # Exit conditions
        conditions = []
        
        # Market no longer sideways (trend detected)
        conditions.append(dataframe['is_sideways'] == False)
        
        # Strong trend detected
        conditions.append(dataframe['adx'] > 40)
        
        # High volatility
        conditions.append(dataframe['volatility'] > 0.05)
        
        # Price breakout from Bollinger Bands
        conditions.append((dataframe['bb_position'] < 0) | (dataframe['bb_position'] > 1))
        
        # Combine exit conditions
        if conditions:
            dataframe.loc[
                reduce(lambda x, y: x | y, conditions),
                ['exit_long', 'exit_tag']
            ] = (1, 'market_change')
        
        return dataframe
    
    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: Optional[str],
        side: str,
        **kwargs
    ) -> bool:
        """
        Final validation before placing an order
        
        Returns:
            True if trade should be placed
        """
        # Get current data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return False
        
        last_candle = dataframe.iloc[-1]
        
        # Check if market conditions are still suitable
        if not self.market_detector.is_suitable_for_trading(last_candle):
            logger.info(f"Market conditions no longer suitable for {pair}")
            return False
        
        # Check risk management rules
        account_balance = self.wallets.get_free(self.config['stake_currency'])
        existing_positions = {
            trade.pair: trade.stake_amount 
            for trade in Trade.get_open_trades()
        }
        
        can_enter, reason = self.risk_manager.evaluate_entry_risk(
            pair=pair,
            entry_price=rate,
            quantity=amount,
            account_balance=account_balance,
            atr=last_candle.get('atr', 0),
            existing_positions=existing_positions
        )
        
        if not can_enter:
            logger.info(f"Risk check failed for {pair}: {reason}")
            return False
        
        # Check if trading should be paused
        if self.risk_manager.should_pause_trading():
            logger.warning("Trading paused due to risk conditions")
            return False
        
        # Validate spread for maker execution
        if 'bid' in kwargs and 'ask' in kwargs:
            if not self.spread_manager.validate_spread(
                bid_price=rate if side == 'buy' else 0,
                ask_price=rate if side == 'sell' else 0,
                best_bid=kwargs['bid'],
                best_ask=kwargs['ask']
            ):
                logger.warning(f"Spread validation failed for {pair}")
                return False
        
        # Log trade attempt
        self.db_logger.log_trade_attempt(
            pair=pair,
            side=side,
            price=rate,
            amount=amount,
            tag=entry_tag,
            timestamp=current_time
        )
        
        logger.info(f"Trade confirmed for {pair}: {side} {amount} @ {rate}")
        return True
    
    def _validate_trade_entry(
        self,
        pair: str,
        amount: float,
        rate: float,
        side: str
    ) -> bool:
        """Implementation of abstract method"""
        return True  # Validation done in confirm_trade_entry
    
    def custom_exit(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs
    ) -> Optional[Union[str, Tuple[str, str]]]:
        """
        Custom exit logic based on risk management and market conditions
        
        Returns:
            Exit reason or None
        """
        # Get current data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None
        
        last_candle = dataframe.iloc[-1]
        
        # Check if market conditions warrant exit
        if self.market_detector.should_exit_positions(last_candle):
            logger.info(f"Market conditions trigger exit for {pair}")
            return 'market_exit'
        
        # Check risk-based exit conditions
        exit_reason = self.risk_manager.evaluate_exit_conditions(
            position_id=str(trade.id),
            current_price=current_rate,
            atr=last_candle.get('atr', 0),
            volatility=last_candle.get('volatility', 0)
        )
        
        if exit_reason:
            logger.info(f"Risk-based exit for {pair}: {exit_reason}")
            return exit_reason
        
        # Check for profit taking in grid strategy
        if current_profit > 0.002:  # 0.2% profit per grid level
            # Check if we should take profit based on grid level
            grid_stats = self.order_manager.get_grid_statistics(pair)
            if grid_stats.get('active_orders', 0) < 2:
                logger.info(f"Taking profit for {pair} at {current_profit:.2%}")
                return 'grid_profit'
        
        return None
    
    def _evaluate_exit_conditions(
        self,
        trade: Trade,
        current_rate: float,
        current_profit: float
    ) -> Optional[str]:
        """Implementation of abstract method"""
        return None  # Exit logic handled in custom_exit
    
    def adjust_trade_position(
        self,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        min_stake: Optional[float],
        max_stake: float,
        **kwargs
    ) -> Optional[float]:
        """
        Adjust position for grid trading (DCA)
        
        Returns:
            Additional amount to buy or None
        """
        # Get current data
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        if dataframe.empty:
            return None
        
        last_candle = dataframe.iloc[-1]
        
        # Only adjust in sideways market
        if not last_candle.get('is_sideways', False):
            return None
        
        # Check if enough time has passed since last order
        min_time_between_orders = 60  # 1 minute minimum
        if trade.pair in self.last_order_time:
            time_since_last = (current_time - self.last_order_time[trade.pair]).seconds
            if time_since_last < min_time_between_orders:
                return None
        
        # Calculate grid levels
        account_balance = self.wallets.get_free(self.config['stake_currency'])
        
        grids = self.order_manager.build_grid(
            pair=trade.pair,
            current_price=current_rate,
            atr=last_candle.get('atr', 0),
            account_balance=account_balance,
            best_bid=last_candle.get('low', current_rate),
            best_ask=last_candle.get('high', current_rate),
            existing_position=trade.amount
        )
        
        # Find appropriate grid level for DCA
        for grid in grids:
            if grid.side.value == 'buy' and grid.price <= current_rate * 0.995:
                # Price dropped enough for next grid level
                adjustment_amount = min(grid.quantity, max_stake)
                
                if adjustment_amount >= min_stake:
                    self.last_order_time[trade.pair] = current_time
                    logger.info(f"Grid adjustment for {trade.pair}: {adjustment_amount} @ {current_rate}")
                    return adjustment_amount
        
        return None
    
    def _calculate_position_adjustment(
        self,
        trade: Trade,
        current_rate: float,
        current_profit: float,
        min_stake: Optional[float],
        max_stake: float
    ) -> Optional[float]:
        """Implementation of abstract method"""
        return None  # Adjustment logic handled in adjust_trade_position
    
    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        side: str,
        **kwargs
    ) -> float:
        """
        Always use 1x leverage for spot trading
        
        Returns:
            1.0 (no leverage)
        """
        return 1.0
    
    def informative_pairs(self):
        """
        Define additional pairs for correlation analysis
        
        Returns:
            List of informative pairs
        """
        # Can add correlated pairs here for better market analysis
        return []
    
    def bot_loop_start(self, **kwargs) -> None:
        """
        Called at the start of each bot loop iteration
        """
        # Reset daily risk metrics if needed
        current_time = datetime.now()
        if current_time.hour == 0 and current_time.minute == 0:
            self.risk_manager.reset_daily_metrics()
            logger.info("Daily metrics reset")
        
        # Update metrics
        self.metrics.update_loop_metrics()
        
        # Clean up stale orders
        for pair in self.order_manager.active_grids.keys():
            stale_orders = self.order_manager.cancel_stale_orders(current_time)
            if stale_orders:
                logger.info(f"Cancelling {len(stale_orders)} stale orders for {pair}")
    
    def confirm_trade_exit(
        self,
        pair: str,
        trade: Trade,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        exit_reason: str,
        current_time: datetime,
        **kwargs
    ) -> bool:
        """
        Final validation before exiting a position
        
        Returns:
            True if exit should proceed
        """
        # Log the exit
        self.db_logger.log_trade_exit(
            trade_id=trade.id,
            pair=pair,
            exit_price=rate,
            exit_reason=exit_reason,
            profit=trade.calc_profit_ratio(rate),
            timestamp=current_time
        )
        
        # Update metrics
        self.metrics.record_trade_exit(
            profit=trade.calc_profit(rate),
            duration=(current_time - trade.open_date_utc).seconds
        )
        
        # Reset grid for this pair
        self.order_manager.reset_grids(pair)
        
        return True


# Required for Freqtrade strategy loading
from functools import reduce
